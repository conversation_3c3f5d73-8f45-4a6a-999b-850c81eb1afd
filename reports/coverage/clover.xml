<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1753450666">
  <project timestamp="1753450666">
    <package name="App\Commands">
      <file name="/Users/<USER>/Herd/validate-links/app/Commands/BaseValidationCommand.php">
        <class name="App\Commands\BaseValidationCommand" namespace="App\Commands">
          <metrics complexity="31" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="86" coveredstatements="0" elements="99" coveredelements="0"/>
        </class>
        <line num="21" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="34" type="method" name="createConfigFromOptions" visibility="protected" complexity="1" crap="2" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="53" type="method" name="parseScopes" visibility="protected" complexity="2" crap="6" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="70" type="method" name="getOutputFormat" visibility="protected" complexity="1" crap="2" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="78" type="method" name="validateCommandOptions" visibility="protected" complexity="4" crap="20" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="101" type="method" name="processValidationResults" visibility="protected" complexity="2" crap="6" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="127" type="method" name="displayResults" visibility="protected" complexity="5" crap="30" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="156" type="method" name="handleValidationError" visibility="protected" complexity="2" crap="6" count="0"/>
        <line num="158" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="170" type="method" name="isValidScopeList" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="172" type="stmt" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="186" type="method" name="groupLinksByStatus" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="188" type="stmt" count="0"/>
        <line num="190" type="stmt" count="0"/>
        <line num="191" type="stmt" count="0"/>
        <line num="192" type="stmt" count="0"/>
        <line num="195" type="stmt" count="0"/>
        <line num="201" type="method" name="displayStatusGroups" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="203" type="stmt" count="0"/>
        <line num="204" type="stmt" count="0"/>
        <line num="205" type="stmt" count="0"/>
        <line num="212" type="method" name="displayStatusGroup" visibility="private" complexity="5" crap="30" count="0"/>
        <line num="214" type="stmt" count="0"/>
        <line num="215" type="stmt" count="0"/>
        <line num="216" type="stmt" count="0"/>
        <line num="218" type="stmt" count="0"/>
        <line num="219" type="stmt" count="0"/>
        <line num="221" type="stmt" count="0"/>
        <line num="222" type="stmt" count="0"/>
        <line num="226" type="stmt" count="0"/>
        <line num="227" type="stmt" count="0"/>
        <line num="234" type="method" name="displayValidationSummary" visibility="private" complexity="1" crap="2" count="0"/>
        <line num="236" type="stmt" count="0"/>
        <line num="237" type="stmt" count="0"/>
        <line num="238" type="stmt" count="0"/>
        <line num="239" type="stmt" count="0"/>
        <line num="240" type="stmt" count="0"/>
        <line num="241" type="stmt" count="0"/>
        <metrics loc="244" ncloc="199" classes="1" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="86" coveredstatements="0" elements="99" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Commands/ConfigCommand.php">
        <class name="App\Commands\ConfigCommand" namespace="App\Commands">
          <metrics complexity="9" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="40" coveredstatements="0" elements="44" coveredelements="0"/>
        </class>
        <line num="21" type="method" name="handle" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="36" type="method" name="initializeConfig" visibility="private" complexity="4" crap="20" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="63" type="method" name="showConfig" visibility="private" complexity="1" crap="2" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="74" type="method" name="gatherConfigurationSettings" visibility="private" complexity="1" crap="2" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <metrics loc="97" ncloc="97" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="40" coveredstatements="0" elements="44" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Commands/FixCommand.php">
        <class name="App\Commands\FixCommand" namespace="App\Commands">
          <metrics complexity="7" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="27" coveredelements="0"/>
        </class>
        <line num="23" type="method" name="handle" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="46" type="method" name="handleInteractiveFix" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="64" type="method" name="handleAutomaticFix" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <metrics loc="80" ncloc="78" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="27" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Commands/InspireCommand.php">
        <class name="App\Commands\InspireCommand" namespace="App\Commands">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="3" coveredstatements="0" elements="5" coveredelements="0"/>
        </class>
        <line num="31" type="method" name="handle" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="46" type="method" name="schedule" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <metrics loc="51" ncloc="34" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="3" coveredstatements="0" elements="5" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Commands/ReportCommand.php">
        <class name="App\Commands\ReportCommand" namespace="App\Commands">
          <metrics complexity="2" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="14" coveredelements="0"/>
        </class>
        <line num="21" type="method" name="handle" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <metrics loc="46" ncloc="46" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="14" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Commands/ValidateCommand.php">
        <class name="App\Commands\ValidateCommand" namespace="App\Commands">
          <metrics complexity="68" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="233" coveredstatements="0" elements="250" coveredelements="0"/>
        </class>
        <line num="49" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="61" type="method" name="handle" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="80" type="method" name="handleInteractive" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="111" type="method" name="gatherPaths" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="138" type="method" name="gatherValidationScope" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="153" type="stmt" count="0"/>
        <line num="154" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="158" type="stmt" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="170" type="method" name="getScopeOptions" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="172" type="stmt" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="186" type="method" name="gatherOutputConfiguration" visibility="private" complexity="5" crap="30" count="0"/>
        <line num="188" type="stmt" count="0"/>
        <line num="189" type="stmt" count="0"/>
        <line num="192" type="stmt" count="0"/>
        <line num="193" type="stmt" count="0"/>
        <line num="194" type="stmt" count="0"/>
        <line num="195" type="stmt" count="0"/>
        <line num="198" type="stmt" count="0"/>
        <line num="200" type="stmt" count="0"/>
        <line num="201" type="stmt" count="0"/>
        <line num="202" type="stmt" count="0"/>
        <line num="205" type="stmt" count="0"/>
        <line num="206" type="stmt" count="0"/>
        <line num="207" type="stmt" count="0"/>
        <line num="208" type="stmt" count="0"/>
        <line num="209" type="stmt" count="0"/>
        <line num="210" type="stmt" count="0"/>
        <line num="212" type="stmt" count="0"/>
        <line num="214" type="stmt" count="0"/>
        <line num="215" type="stmt" count="0"/>
        <line num="216" type="stmt" count="0"/>
        <line num="218" type="stmt" count="0"/>
        <line num="219" type="stmt" count="0"/>
        <line num="220" type="stmt" count="0"/>
        <line num="221" type="stmt" count="0"/>
        <line num="222" type="stmt" count="0"/>
        <line num="223" type="stmt" count="0"/>
        <line num="224" type="stmt" count="0"/>
        <line num="225" type="stmt" count="0"/>
        <line num="226" type="stmt" count="0"/>
        <line num="230" type="stmt" count="0"/>
        <line num="231" type="stmt" count="0"/>
        <line num="232" type="stmt" count="0"/>
        <line num="233" type="stmt" count="0"/>
        <line num="239" type="method" name="validateOutputPath" visibility="private" complexity="4" crap="20" count="0"/>
        <line num="241" type="stmt" count="0"/>
        <line num="242" type="stmt" count="0"/>
        <line num="244" type="stmt" count="0"/>
        <line num="245" type="stmt" count="0"/>
        <line num="248" type="stmt" count="0"/>
        <line num="249" type="stmt" count="0"/>
        <line num="250" type="stmt" count="0"/>
        <line num="253" type="stmt" count="0"/>
        <line num="259" type="method" name="gatherAdvancedOptions" visibility="private" complexity="12" crap="156" count="0"/>
        <line num="261" type="stmt" count="0"/>
        <line num="263" type="stmt" count="0"/>
        <line num="265" type="stmt" count="0"/>
        <line num="266" type="stmt" count="0"/>
        <line num="267" type="stmt" count="0"/>
        <line num="268" type="stmt" count="0"/>
        <line num="269" type="stmt" count="0"/>
        <line num="270" type="stmt" count="0"/>
        <line num="271" type="stmt" count="0"/>
        <line num="272" type="stmt" count="0"/>
        <line num="273" type="stmt" count="0"/>
        <line num="276" type="stmt" count="0"/>
        <line num="277" type="stmt" count="0"/>
        <line num="278" type="stmt" count="0"/>
        <line num="279" type="stmt" count="0"/>
        <line num="280" type="stmt" count="0"/>
        <line num="281" type="stmt" count="0"/>
        <line num="282" type="stmt" count="0"/>
        <line num="283" type="stmt" count="0"/>
        <line num="285" type="stmt" count="0"/>
        <line num="287" type="stmt" count="0"/>
        <line num="288" type="stmt" count="0"/>
        <line num="289" type="stmt" count="0"/>
        <line num="290" type="stmt" count="0"/>
        <line num="291" type="stmt" count="0"/>
        <line num="292" type="stmt" count="0"/>
        <line num="293" type="stmt" count="0"/>
        <line num="294" type="stmt" count="0"/>
        <line num="295" type="stmt" count="0"/>
        <line num="298" type="stmt" count="0"/>
        <line num="300" type="stmt" count="0"/>
        <line num="306" type="method" name="confirmConfiguration" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="308" type="stmt" count="0"/>
        <line num="309" type="stmt" count="0"/>
        <line num="310" type="stmt" count="0"/>
        <line num="311" type="stmt" count="0"/>
        <line num="312" type="stmt" count="0"/>
        <line num="313" type="stmt" count="0"/>
        <line num="314" type="stmt" count="0"/>
        <line num="316" type="stmt" count="0"/>
        <line num="317" type="stmt" count="0"/>
        <line num="318" type="stmt" count="0"/>
        <line num="320" type="stmt" count="0"/>
        <line num="326" type="method" name="executeInteractiveValidation" visibility="private" complexity="6" crap="42" count="0"/>
        <line num="328" type="stmt" count="0"/>
        <line num="330" type="stmt" count="0"/>
        <line num="331" type="stmt" count="0"/>
        <line num="332" type="stmt" count="0"/>
        <line num="333" type="stmt" count="0"/>
        <line num="335" type="stmt" count="0"/>
        <line num="336" type="stmt" count="0"/>
        <line num="338" type="stmt" count="0"/>
        <line num="339" type="stmt" count="0"/>
        <line num="341" type="stmt" count="0"/>
        <line num="342" type="stmt" count="0"/>
        <line num="344" type="stmt" count="0"/>
        <line num="345" type="stmt" count="0"/>
        <line num="347" type="stmt" count="0"/>
        <line num="348" type="stmt" count="0"/>
        <line num="351" type="stmt" count="0"/>
        <line num="352" type="stmt" count="0"/>
        <line num="357" type="stmt" count="0"/>
        <line num="360" type="stmt" count="0"/>
        <line num="362" type="stmt" count="0"/>
        <line num="363" type="stmt" count="0"/>
        <line num="364" type="stmt" count="0"/>
        <line num="366" type="stmt" count="0"/>
        <line num="370" type="stmt" count="0"/>
        <line num="371" type="stmt" count="0"/>
        <line num="373" type="stmt" count="0"/>
        <line num="379" type="method" name="countFiles" visibility="private" complexity="4" crap="20" count="0"/>
        <line num="381" type="stmt" count="0"/>
        <line num="382" type="stmt" count="0"/>
        <line num="383" type="stmt" count="0"/>
        <line num="384" type="stmt" count="0"/>
        <line num="385" type="stmt" count="0"/>
        <line num="386" type="stmt" count="0"/>
        <line num="390" type="stmt" count="0"/>
        <line num="396" type="method" name="getFilesFromPath" visibility="private" complexity="5" crap="30" count="0"/>
        <line num="398" type="stmt" count="0"/>
        <line num="399" type="stmt" count="0"/>
        <line num="402" type="stmt" count="0"/>
        <line num="403" type="stmt" count="0"/>
        <line num="404" type="stmt" count="0"/>
        <line num="405" type="stmt" count="0"/>
        <line num="407" type="stmt" count="0"/>
        <line num="408" type="stmt" count="0"/>
        <line num="409" type="stmt" count="0"/>
        <line num="413" type="stmt" count="0"/>
        <line num="419" type="method" name="hasBrokenLinks" visibility="private" complexity="4" crap="20" count="0"/>
        <line num="421" type="stmt" count="0"/>
        <line num="422" type="stmt" count="0"/>
        <line num="423" type="stmt" count="0"/>
        <line num="427" type="stmt" count="0"/>
        <line num="433" type="method" name="displaySummary" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="435" type="stmt" count="0"/>
        <line num="436" type="stmt" count="0"/>
        <line num="437" type="stmt" count="0"/>
        <line num="438" type="stmt" count="0"/>
        <line num="439" type="stmt" count="0"/>
        <line num="441" type="stmt" count="0"/>
        <line num="442" type="stmt" count="0"/>
        <line num="444" type="stmt" count="0"/>
        <line num="447" type="stmt" count="0"/>
        <line num="453" type="method" name="handleNonInteractive" visibility="private" complexity="1" crap="2" count="0"/>
        <line num="455" type="stmt" count="0"/>
        <line num="456" type="stmt" count="0"/>
        <line num="457" type="stmt" count="0"/>
        <line num="458" type="stmt" count="0"/>
        <line num="459" type="stmt" count="0"/>
        <line num="460" type="stmt" count="0"/>
        <line num="462" type="stmt" count="0"/>
        <line num="468" type="method" name="performValidation" visibility="private" complexity="7" crap="56" count="0"/>
        <line num="476" type="stmt" count="0"/>
        <line num="477" type="stmt" count="0"/>
        <line num="478" type="stmt" count="0"/>
        <line num="480" type="stmt" count="0"/>
        <line num="481" type="stmt" count="0"/>
        <line num="484" type="stmt" count="0"/>
        <line num="488" type="stmt" count="0"/>
        <line num="489" type="stmt" count="0"/>
        <line num="490" type="stmt" count="0"/>
        <line num="491" type="stmt" count="0"/>
        <line num="492" type="stmt" count="0"/>
        <line num="495" type="stmt" count="0"/>
        <line num="498" type="stmt" count="0"/>
        <line num="500" type="stmt" count="0"/>
        <line num="501" type="stmt" count="0"/>
        <line num="503" type="stmt" count="0"/>
        <line num="507" type="stmt" count="0"/>
        <line num="508" type="stmt" count="0"/>
        <line num="510" type="stmt" count="0"/>
        <line num="511" type="stmt" count="0"/>
        <line num="513" type="stmt" count="0"/>
        <line num="515" type="stmt" count="0"/>
        <line num="520" type="stmt" count="0"/>
        <line num="521" type="stmt" count="0"/>
        <line num="522" type="stmt" count="0"/>
        <line num="523" type="stmt" count="0"/>
        <line num="524" type="stmt" count="0"/>
        <line num="525" type="stmt" count="0"/>
        <line num="526" type="stmt" count="0"/>
        <line num="528" type="stmt" count="0"/>
        <line num="529" type="stmt" count="0"/>
        <line num="530" type="stmt" count="0"/>
        <line num="532" type="stmt" count="0"/>
        <metrics loc="536" ncloc="462" classes="1" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="233" coveredstatements="0" elements="250" coveredelements="0"/>
      </file>
    </package>
    <file name="/Users/<USER>/Herd/validate-links/app/Contracts/ExtensionInterface.php">
      <metrics loc="21" ncloc="20" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <package name="App\Enums">
      <file name="/Users/<USER>/Herd/validate-links/app/Enums/LinkStatus.php">
        <class name="App\Enums\LinkStatus" namespace="App\Enums">
          <metrics complexity="72" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="85" coveredstatements="0" elements="98" coveredelements="0"/>
        </class>
        <line num="23" type="method" name="values" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="31" type="method" name="isBroken" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="39" type="method" name="isTemporary" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="50" type="method" name="isSecurityIssue" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="61" type="method" name="getDescription" visibility="public" complexity="11" crap="132" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="80" type="method" name="getSeverity" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="94" type="method" name="getRecommendedAction" visibility="public" complexity="11" crap="132" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="113" type="method" name="getConsoleColor" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="127" type="method" name="getIcon" visibility="public" complexity="11" crap="132" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="146" type="method" name="getGroup" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="153" type="stmt" count="0"/>
        <line num="154" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="161" type="method" name="getFormattedDisplay" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="167" type="stmt" count="0"/>
        <line num="173" type="method" name="shouldRetry" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="184" type="method" name="getHttpStatusCode" visibility="public" complexity="8" crap="72" count="0"/>
        <line num="186" type="stmt" count="0"/>
        <line num="187" type="stmt" count="0"/>
        <line num="188" type="stmt" count="0"/>
        <line num="189" type="stmt" count="0"/>
        <line num="190" type="stmt" count="0"/>
        <line num="191" type="stmt" count="0"/>
        <line num="192" type="stmt" count="0"/>
        <line num="193" type="stmt" count="0"/>
        <line num="194" type="stmt" count="0"/>
        <metrics loc="197" ncloc="158" classes="1" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="85" coveredstatements="0" elements="98" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Enums/OutputFormat.php">
        <class name="App\Enums\OutputFormat" namespace="App\Enums">
          <metrics complexity="61" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="77" coveredstatements="0" elements="90" coveredelements="0"/>
        </class>
        <line num="19" type="method" name="values" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="27" type="method" name="getSelectOptions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="41" type="method" name="isValid" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="49" type="method" name="getExtension" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="64" type="method" name="getMimeType" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="79" type="method" name="isStructured" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="90" type="method" name="supportsFormatting" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="101" type="method" name="getFormatterClass" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="116" type="method" name="getDefaultFilename" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="131" type="method" name="isCiCdFriendly" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="142" type="method" name="getDescription" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="157" type="method" name="getHelpText" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="159" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="166" type="stmt" count="0"/>
        <line num="172" type="method" name="getUseCases" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <metrics loc="184" ncloc="145" classes="1" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="77" coveredstatements="0" elements="90" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Enums/ValidationScope.php">
        <class name="App\Enums\ValidationScope" namespace="App\Enums">
          <metrics complexity="34" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="40" coveredstatements="0" elements="53" coveredelements="0"/>
        </class>
        <line num="20" type="method" name="values" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="28" type="method" name="names" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="36" type="method" name="getSelectOptions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="50" type="method" name="isValid" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="58" type="method" name="fromString" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="66" type="method" name="includesExternal" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="74" type="method" name="includesInternal" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="82" type="method" name="includesAnchor" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="90" type="method" name="includesImage" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="98" type="method" name="getDescription" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="112" type="method" name="getHelpText" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="126" type="method" name="getIncludedScopes" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="137" type="method" name="getPriority" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="142" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <metrics loc="148" ncloc="109" classes="1" methods="13" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="40" coveredstatements="0" elements="53" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Exceptions">
      <file name="/Users/<USER>/Herd/validate-links/app/Exceptions/ConfigurationException.php">
        <class name="App\Exceptions\ConfigurationException" namespace="App\Exceptions">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="getErrorCode" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="getSeverity" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <metrics loc="22" ncloc="19" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Exceptions/Handler.php">
        <class name="App\Exceptions\Handler" namespace="App\Exceptions">
          <metrics complexity="6" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="12" coveredstatements="0" elements="14" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="render" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="23" type="method" name="renderValidateLinksException" visibility="private" complexity="4" crap="20" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <metrics loc="42" ncloc="41" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="12" coveredstatements="0" elements="14" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Exceptions/SecurityException.php">
        <class name="App\Exceptions\SecurityException" namespace="App\Exceptions">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="getErrorCode" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="getSeverity" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <metrics loc="22" ncloc="19" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Exceptions/ValidateLinksException.php">
        <class name="App\Exceptions\ValidateLinksException" namespace="App\Exceptions">
          <metrics complexity="2" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="15" coveredelements="0"/>
        </class>
        <line num="17" type="method" name="getErrorCode" visibility="public" complexity="0" crap="0" count="0"/>
        <line num="22" type="method" name="getSeverity" visibility="public" complexity="0" crap="0" count="0"/>
        <line num="27" type="method" name="getContext" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="35" type="method" name="toArray" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <metrics loc="47" ncloc="32" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="15" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Exceptions/ValidationException.php">
        <class name="App\Exceptions\ValidationException" namespace="App\Exceptions">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="getErrorCode" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="getSeverity" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <metrics loc="22" ncloc="19" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Providers">
      <file name="/Users/<USER>/Herd/validate-links/app/Providers/AppServiceProvider.php">
        <class name="App\Providers\AppServiceProvider" namespace="App\Providers">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="boot" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="22" type="method" name="register" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <metrics loc="27" ncloc="19" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Providers/ValidateLinksServiceProvider.php">
        <class name="App\Providers\ValidateLinksServiceProvider" namespace="App\Providers">
          <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="27" coveredelements="0"/>
        </class>
        <line num="27" type="method" name="register" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="62" type="method" name="boot" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="76" type="method" name="provides" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <metrics loc="87" ncloc="63" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="27" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Services">
      <file name="/Users/<USER>/Herd/validate-links/app/Services/ConcurrentValidationService.php">
        <class name="App\Services\ConcurrentValidationService" namespace="App\Services">
          <metrics complexity="4" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="23" coveredstatements="0" elements="26" coveredelements="0"/>
        </class>
        <line num="20" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="31" type="method" name="validateExternalLinksConcurrently" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="52" type="method" name="createRequests" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <metrics loc="59" ncloc="58" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="23" coveredstatements="0" elements="26" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/GitHubAnchorService.php">
        <class name="App\Services\GitHubAnchorService" namespace="App\Services">
          <metrics complexity="10" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="26" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="generateAnchor" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="37" type="method" name="validateAnchor" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="48" type="method" name="extractAnchors" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="79" type="method" name="normalizeAnchor" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <metrics loc="88" ncloc="66" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="26" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/LinkValidationService.php">
        <class name="App\Services\LinkValidationService" namespace="App\Services">
          <metrics complexity="27" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="79" coveredstatements="0" elements="86" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="20" type="method" name="validateFile" visibility="public" complexity="8" crap="72" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="51" type="method" name="extractLinks" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="78" type="method" name="categorizeLinks" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="102" type="method" name="validateCrossReferences" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="120" type="method" name="extractAllAnchors" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="133" type="method" name="validateCrossReference" visibility="private" complexity="4" crap="20" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="142" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="158" type="stmt" count="0"/>
        <line num="159" type="stmt" count="0"/>
        <metrics loc="162" ncloc="157" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="79" coveredstatements="0" elements="86" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/PluginManager.php">
        <class name="App\Services\PluginManager" namespace="App\Services">
          <metrics complexity="4" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="7" coveredstatements="0" elements="11" coveredelements="0"/>
        </class>
        <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="21" type="method" name="register" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="27" type="method" name="boot" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="34" type="method" name="getPlugin" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <metrics loc="39" ncloc="38" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="7" coveredstatements="0" elements="11" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/ReportingService.php">
        <class name="App\Services\ReportingService" namespace="App\Services">
          <metrics complexity="14" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="40" coveredstatements="0" elements="46" coveredelements="0"/>
        </class>
        <line num="23" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="37" type="method" name="generateReport" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="52" type="method" name="addFormatter" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="60" type="method" name="exportReport" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="79" type="method" name="getAvailableFormats" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="87" type="method" name="generateSummary" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <metrics loc="117" ncloc="102" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="40" coveredstatements="0" elements="46" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/SecurityValidationService.php">
        <class name="App\Services\SecurityValidationService" namespace="App\Services">
          <metrics complexity="25" methods="8" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="55" coveredstatements="0" elements="63" coveredelements="0"/>
        </class>
        <line num="20" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="39" type="method" name="validateUrl" visibility="public" complexity="9" crap="90" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="74" type="method" name="validatePath" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="105" type="method" name="isDomainBlocked" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="113" type="method" name="isProtocolAllowed" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="121" type="method" name="validateFileSize" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="139" type="method" name="getSecurityConfig" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="142" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="152" type="method" name="isPrivateIp" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="154" type="stmt" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <metrics loc="163" ncloc="136" classes="1" methods="8" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="55" coveredstatements="0" elements="63" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/StatisticsService.php">
        <class name="App\Services\StatisticsService" namespace="App\Services">
          <metrics complexity="10" methods="8" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="37" coveredstatements="0" elements="45" coveredelements="0"/>
        </class>
        <line num="17" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="25" type="method" name="recordValidation" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="44" type="method" name="recordFileProcessed" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="59" type="method" name="getStatistics" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="71" type="method" name="getBrokenLinks" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="79" type="method" name="getProcessedFiles" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="87" type="method" name="reset" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="105" type="method" name="getSuccessRate" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <metrics loc="114" ncloc="93" classes="1" methods="8" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="37" coveredstatements="0" elements="45" coveredelements="0"/>
      </file>
    </package>
    <file name="/Users/<USER>/Herd/validate-links/app/Services/Contracts/GitHubAnchorInterface.php">
      <metrics loc="29" ncloc="17" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Herd/validate-links/app/Services/Contracts/LinkValidationInterface.php">
      <metrics loc="83" ncloc="36" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Herd/validate-links/app/Services/Contracts/ReportingInterface.php">
      <metrics loc="60" ncloc="29" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Herd/validate-links/app/Services/Contracts/SecurityValidationInterface.php">
      <metrics loc="39" ncloc="21" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Herd/validate-links/app/Services/Contracts/StatisticsInterface.php">
      <metrics loc="44" ncloc="23" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <package name="App\Services\Formatters">
      <file name="/Users/<USER>/Herd/validate-links/app/Services/Formatters/ConsoleFormatter.php">
        <class name="App\Services\Formatters\ConsoleFormatter" namespace="App\Services\Formatters">
          <metrics complexity="12" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="34" coveredstatements="0" elements="39" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="format" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="35" type="method" name="formatHeader" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="43" type="method" name="formatSummary" visibility="private" complexity="1" crap="2" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="57" type="method" name="formatBrokenLinks" visibility="private" complexity="2" crap="6" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="71" type="method" name="formatStatistics" visibility="private" complexity="4" crap="20" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <metrics loc="89" ncloc="82" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="34" coveredstatements="0" elements="39" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/Formatters/HtmlFormatter.php">
        <class name="App\Services\Formatters\HtmlFormatter" namespace="App\Services\Formatters">
          <metrics complexity="11" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="55" coveredstatements="0" elements="58" coveredelements="0"/>
        </class>
        <line num="11" type="method" name="format" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="19" type="method" name="generateContent" visibility="private" complexity="9" crap="90" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="99" type="method" name="getHtmlTemplate" visibility="private" complexity="1" crap="2" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="209" type="stmt" count="0"/>
        <metrics loc="212" ncloc="208" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="55" coveredstatements="0" elements="58" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/Formatters/JsonFormatter.php">
        <class name="App\Services\Formatters\JsonFormatter" namespace="App\Services\Formatters">
          <metrics complexity="2" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="23" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="format" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <metrics loc="41" ncloc="38" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="23" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/Formatters/MarkdownFormatter.php">
        <class name="App\Services\Formatters\MarkdownFormatter" namespace="App\Services\Formatters">
          <metrics complexity="6" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="36" coveredstatements="0" elements="37" coveredelements="0"/>
        </class>
        <line num="11" type="method" name="format" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <metrics loc="68" ncloc="65" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="36" coveredstatements="0" elements="37" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Services\ValueObjects">
      <file name="/Users/<USER>/Herd/validate-links/app/Services/ValueObjects/ValidationConfig.php">
        <class name="App\Services\ValueObjects\ValidationConfig" namespace="App\Services\ValueObjects">
          <metrics complexity="33" methods="22" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="75" coveredstatements="0" elements="97" coveredelements="0"/>
        </class>
        <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="33" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="51" type="method" name="withDefaults" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="61" type="method" name="getScopes" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="69" type="method" name="hasScope" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="80" type="method" name="getEffectiveScopes" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="89" type="method" name="getTimeout" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="94" type="method" name="getMaxRedirects" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="99" type="method" name="getConcurrentRequests" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="104" type="method" name="isCacheEnabled" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="109" type="method" name="shouldFollowRedirects" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="114" type="method" name="getUserAgent" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="119" type="method" name="getOutputFormat" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="124" type="method" name="getAdditionalOptions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="129" type="method" name="shouldValidateInternal" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="134" type="method" name="shouldValidateExternal" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="139" type="method" name="shouldValidateAnchors" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="144" type="method" name="shouldValidateCrossReferences" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="154" type="method" name="withScopes" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="158" type="stmt" count="0"/>
        <line num="159" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="166" type="stmt" count="0"/>
        <line num="172" type="method" name="withOutputFormat" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <line num="182" type="stmt" count="0"/>
        <line num="183" type="stmt" count="0"/>
        <line num="184" type="stmt" count="0"/>
        <line num="187" type="method" name="toArray" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="189" type="stmt" count="0"/>
        <line num="190" type="stmt" count="0"/>
        <line num="191" type="stmt" count="0"/>
        <line num="192" type="stmt" count="0"/>
        <line num="193" type="stmt" count="0"/>
        <line num="194" type="stmt" count="0"/>
        <line num="195" type="stmt" count="0"/>
        <line num="196" type="stmt" count="0"/>
        <line num="197" type="stmt" count="0"/>
        <line num="198" type="stmt" count="0"/>
        <line num="199" type="stmt" count="0"/>
        <line num="202" type="method" name="validate" visibility="private" complexity="10" crap="110" count="0"/>
        <line num="204" type="stmt" count="0"/>
        <line num="205" type="stmt" count="0"/>
        <line num="208" type="stmt" count="0"/>
        <line num="209" type="stmt" count="0"/>
        <line num="210" type="stmt" count="0"/>
        <line num="214" type="stmt" count="0"/>
        <line num="215" type="stmt" count="0"/>
        <line num="218" type="stmt" count="0"/>
        <line num="219" type="stmt" count="0"/>
        <line num="222" type="stmt" count="0"/>
        <line num="223" type="stmt" count="0"/>
        <metrics loc="227" ncloc="197" classes="1" methods="22" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="75" coveredstatements="0" elements="97" coveredelements="0"/>
      </file>
      <file name="/Users/<USER>/Herd/validate-links/app/Services/ValueObjects/ValidationResult.php">
        <class name="App\Services\ValueObjects\ValidationResult" namespace="App\Services\ValueObjects">
          <metrics complexity="22" methods="22" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="51" coveredstatements="0" elements="73" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="26" type="method" name="success" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="46" type="method" name="failure" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="68" type="method" name="getUrl" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="73" type="method" name="getStatus" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="78" type="method" name="getScope" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="83" type="method" name="isValid" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="88" type="method" name="isBroken" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="93" type="method" name="isTemporary" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="98" type="method" name="isSecurityIssue" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="103" type="method" name="getError" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="108" type="method" name="getHttpStatusCode" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="113" type="method" name="getRedirectUrl" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="118" type="method" name="getResponseTime" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="123" type="method" name="getMetadata" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="131" type="method" name="getSeverity" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="139" type="method" name="getRecommendedAction" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="147" type="method" name="getConsoleColor" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="155" type="method" name="getFormattedDisplay" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="163" type="method" name="shouldRetry" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="168" type="method" name="toArray" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="170" type="stmt" count="0"/>
        <line num="171" type="stmt" count="0"/>
        <line num="172" type="stmt" count="0"/>
        <line num="173" type="stmt" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <line num="182" type="stmt" count="0"/>
        <line num="183" type="stmt" count="0"/>
        <line num="189" type="method" name="toJson" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="191" type="stmt" count="0"/>
        <metrics loc="194" ncloc="170" classes="1" methods="22" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="51" coveredstatements="0" elements="73" coveredelements="0"/>
      </file>
    </package>
    <metrics files="35" loc="3701" ncloc="3096" classes="29" methods="190" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1192" coveredstatements="0" elements="1382" coveredelements="0"/>
  </project>
</coverage>
