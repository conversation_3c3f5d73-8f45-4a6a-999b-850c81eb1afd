<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Herd/validate-links/app</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="_css/nv.d3.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/Users/<USER>/Herd/validate-links/app</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#15">App\Commands\BaseValidationCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#13">App\Commands\ConfigCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#13">App\Commands\FixCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/InspireCommand.php.html#12">App\Commands\InspireCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ReportCommand.php.html#11">App\Commands\ReportCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#21">App\Commands\ValidateCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ConfigurationException.php.html#10">App\Exceptions\ConfigurationException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/Handler.php.html#12">App\Exceptions\Handler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/SecurityException.php.html#10">App\Exceptions\SecurityException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidateLinksException.php.html#12">App\Exceptions\ValidateLinksException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidationException.php.html#10">App\Exceptions\ValidationException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/AppServiceProvider.php.html#9">App\Providers\AppServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/ValidateLinksServiceProvider.php.html#19">App\Providers\ValidateLinksServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ConcurrentValidationService.php.html#14">App\Services\ConcurrentValidationService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#9">App\Services\Formatters\ConsoleFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/HtmlFormatter.php.html#9">App\Services\Formatters\HtmlFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/JsonFormatter.php.html#9">App\Services\Formatters\JsonFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/MarkdownFormatter.php.html#9">App\Services\Formatters\MarkdownFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/GitHubAnchorService.php.html#9">App\Services\GitHubAnchorService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#12">App\Services\LinkValidationService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/PluginManager.php.html#12">App\Services\PluginManager</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#17">App\Services\ReportingService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#10">App\Services\SecurityValidationService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#9">App\Services\StatisticsService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#11">App\Services\ValueObjects\ValidationConfig</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#10">App\Services\ValueObjects\ValidationResult</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#7">App\Enums\LinkStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#7">App\Enums\OutputFormat</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#9">App\Enums\ValidationScope</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Enums/LinkStatus.php.html#7">App\Enums\LinkStatus</a></td><td class="text-right">5256</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#21">App\Commands\ValidateCommand</a></td><td class="text-right">4692</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#7">App\Enums\OutputFormat</a></td><td class="text-right">3782</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#9">App\Enums\ValidationScope</a></td><td class="text-right">1190</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#11">App\Services\ValueObjects\ValidationConfig</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#15">App\Commands\BaseValidationCommand</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#12">App\Services\LinkValidationService</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#10">App\Services\SecurityValidationService</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#17">App\Services\ReportingService</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#9">App\Services\Formatters\ConsoleFormatter</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Services/Formatters/HtmlFormatter.php.html#9">App\Services\Formatters\HtmlFormatter</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Services/GitHubAnchorService.php.html#9">App\Services\GitHubAnchorService</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#9">App\Services\StatisticsService</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#13">App\Commands\ConfigCommand</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#13">App\Commands\FixCommand</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Exceptions/Handler.php.html#12">App\Exceptions\Handler</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/Formatters/MarkdownFormatter.php.html#9">App\Services\Formatters\MarkdownFormatter</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/ConcurrentValidationService.php.html#14">App\Services\ConcurrentValidationService</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/ReportCommand.php.html#11">App\Commands\ReportCommand</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Formatters/JsonFormatter.php.html#9">App\Services\Formatters\JsonFormatter</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#21"><abbr title="App\Commands\BaseValidationCommand::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#34"><abbr title="App\Commands\BaseValidationCommand::createConfigFromOptions">createConfigFromOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#53"><abbr title="App\Commands\BaseValidationCommand::parseScopes">parseScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#70"><abbr title="App\Commands\BaseValidationCommand::getOutputFormat">getOutputFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#78"><abbr title="App\Commands\BaseValidationCommand::validateCommandOptions">validateCommandOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#101"><abbr title="App\Commands\BaseValidationCommand::processValidationResults">processValidationResults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#127"><abbr title="App\Commands\BaseValidationCommand::displayResults">displayResults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#156"><abbr title="App\Commands\BaseValidationCommand::handleValidationError">handleValidationError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#170"><abbr title="App\Commands\BaseValidationCommand::isValidScopeList">isValidScopeList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#186"><abbr title="App\Commands\BaseValidationCommand::groupLinksByStatus">groupLinksByStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#201"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroups">displayStatusGroups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#212"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroup">displayStatusGroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#234"><abbr title="App\Commands\BaseValidationCommand::displayValidationSummary">displayValidationSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#21"><abbr title="App\Commands\ConfigCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#36"><abbr title="App\Commands\ConfigCommand::initializeConfig">initializeConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#63"><abbr title="App\Commands\ConfigCommand::showConfig">showConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#74"><abbr title="App\Commands\ConfigCommand::gatherConfigurationSettings">gatherConfigurationSettings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#23"><abbr title="App\Commands\FixCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#46"><abbr title="App\Commands\FixCommand::handleInteractiveFix">handleInteractiveFix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#64"><abbr title="App\Commands\FixCommand::handleAutomaticFix">handleAutomaticFix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/InspireCommand.php.html#31"><abbr title="App\Commands\InspireCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/InspireCommand.php.html#46"><abbr title="App\Commands\InspireCommand::schedule">schedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ReportCommand.php.html#21"><abbr title="App\Commands\ReportCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#49"><abbr title="App\Commands\ValidateCommand::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#61"><abbr title="App\Commands\ValidateCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#80"><abbr title="App\Commands\ValidateCommand::handleInteractive">handleInteractive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#111"><abbr title="App\Commands\ValidateCommand::gatherPaths">gatherPaths</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#138"><abbr title="App\Commands\ValidateCommand::gatherValidationScope">gatherValidationScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#170"><abbr title="App\Commands\ValidateCommand::getScopeOptions">getScopeOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#186"><abbr title="App\Commands\ValidateCommand::gatherOutputConfiguration">gatherOutputConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#239"><abbr title="App\Commands\ValidateCommand::validateOutputPath">validateOutputPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#259"><abbr title="App\Commands\ValidateCommand::gatherAdvancedOptions">gatherAdvancedOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#306"><abbr title="App\Commands\ValidateCommand::confirmConfiguration">confirmConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#326"><abbr title="App\Commands\ValidateCommand::executeInteractiveValidation">executeInteractiveValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#379"><abbr title="App\Commands\ValidateCommand::countFiles">countFiles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#396"><abbr title="App\Commands\ValidateCommand::getFilesFromPath">getFilesFromPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#419"><abbr title="App\Commands\ValidateCommand::hasBrokenLinks">hasBrokenLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#433"><abbr title="App\Commands\ValidateCommand::displaySummary">displaySummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#453"><abbr title="App\Commands\ValidateCommand::handleNonInteractive">handleNonInteractive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#468"><abbr title="App\Commands\ValidateCommand::performValidation">performValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ConfigurationException.php.html#12"><abbr title="App\Exceptions\ConfigurationException::getErrorCode">getErrorCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ConfigurationException.php.html#17"><abbr title="App\Exceptions\ConfigurationException::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/Handler.php.html#14"><abbr title="App\Exceptions\Handler::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/Handler.php.html#23"><abbr title="App\Exceptions\Handler::renderValidateLinksException">renderValidateLinksException</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/SecurityException.php.html#12"><abbr title="App\Exceptions\SecurityException::getErrorCode">getErrorCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/SecurityException.php.html#17"><abbr title="App\Exceptions\SecurityException::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidateLinksException.php.html#17"><abbr title="App\Exceptions\ValidateLinksException::getErrorCode">getErrorCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidateLinksException.php.html#22"><abbr title="App\Exceptions\ValidateLinksException::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidateLinksException.php.html#27"><abbr title="App\Exceptions\ValidateLinksException::getContext">getContext</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidateLinksException.php.html#35"><abbr title="App\Exceptions\ValidateLinksException::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidationException.php.html#12"><abbr title="App\Exceptions\ValidationException::getErrorCode">getErrorCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Exceptions/ValidationException.php.html#17"><abbr title="App\Exceptions\ValidationException::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/AppServiceProvider.php.html#14"><abbr title="App\Providers\AppServiceProvider::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/AppServiceProvider.php.html#22"><abbr title="App\Providers\AppServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/ValidateLinksServiceProvider.php.html#27"><abbr title="App\Providers\ValidateLinksServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/ValidateLinksServiceProvider.php.html#62"><abbr title="App\Providers\ValidateLinksServiceProvider::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/ValidateLinksServiceProvider.php.html#76"><abbr title="App\Providers\ValidateLinksServiceProvider::provides">provides</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ConcurrentValidationService.php.html#20"><abbr title="App\Services\ConcurrentValidationService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ConcurrentValidationService.php.html#31"><abbr title="App\Services\ConcurrentValidationService::validateExternalLinksConcurrently">validateExternalLinksConcurrently</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ConcurrentValidationService.php.html#52"><abbr title="App\Services\ConcurrentValidationService::createRequests">createRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#14"><abbr title="App\Services\Formatters\ConsoleFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#35"><abbr title="App\Services\Formatters\ConsoleFormatter::formatHeader">formatHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#43"><abbr title="App\Services\Formatters\ConsoleFormatter::formatSummary">formatSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#57"><abbr title="App\Services\Formatters\ConsoleFormatter::formatBrokenLinks">formatBrokenLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#71"><abbr title="App\Services\Formatters\ConsoleFormatter::formatStatistics">formatStatistics</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/HtmlFormatter.php.html#11"><abbr title="App\Services\Formatters\HtmlFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/HtmlFormatter.php.html#19"><abbr title="App\Services\Formatters\HtmlFormatter::generateContent">generateContent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/HtmlFormatter.php.html#99"><abbr title="App\Services\Formatters\HtmlFormatter::getHtmlTemplate">getHtmlTemplate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/JsonFormatter.php.html#14"><abbr title="App\Services\Formatters\JsonFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Formatters/MarkdownFormatter.php.html#11"><abbr title="App\Services\Formatters\MarkdownFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/GitHubAnchorService.php.html#14"><abbr title="App\Services\GitHubAnchorService::generateAnchor">generateAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/GitHubAnchorService.php.html#37"><abbr title="App\Services\GitHubAnchorService::validateAnchor">validateAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/GitHubAnchorService.php.html#48"><abbr title="App\Services\GitHubAnchorService::extractAnchors">extractAnchors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/GitHubAnchorService.php.html#79"><abbr title="App\Services\GitHubAnchorService::normalizeAnchor">normalizeAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#14"><abbr title="App\Services\LinkValidationService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#20"><abbr title="App\Services\LinkValidationService::validateFile">validateFile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#51"><abbr title="App\Services\LinkValidationService::extractLinks">extractLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#78"><abbr title="App\Services\LinkValidationService::categorizeLinks">categorizeLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#102"><abbr title="App\Services\LinkValidationService::validateCrossReferences">validateCrossReferences</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#120"><abbr title="App\Services\LinkValidationService::extractAllAnchors">extractAllAnchors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#133"><abbr title="App\Services\LinkValidationService::validateCrossReference">validateCrossReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/PluginManager.php.html#16"><abbr title="App\Services\PluginManager::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/PluginManager.php.html#21"><abbr title="App\Services\PluginManager::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/PluginManager.php.html#27"><abbr title="App\Services\PluginManager::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/PluginManager.php.html#34"><abbr title="App\Services\PluginManager::getPlugin">getPlugin</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#23"><abbr title="App\Services\ReportingService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#37"><abbr title="App\Services\ReportingService::generateReport">generateReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#52"><abbr title="App\Services\ReportingService::addFormatter">addFormatter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#60"><abbr title="App\Services\ReportingService::exportReport">exportReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#79"><abbr title="App\Services\ReportingService::getAvailableFormats">getAvailableFormats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#87"><abbr title="App\Services\ReportingService::generateSummary">generateSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#20"><abbr title="App\Services\SecurityValidationService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#39"><abbr title="App\Services\SecurityValidationService::validateUrl">validateUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#74"><abbr title="App\Services\SecurityValidationService::validatePath">validatePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#105"><abbr title="App\Services\SecurityValidationService::isDomainBlocked">isDomainBlocked</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#113"><abbr title="App\Services\SecurityValidationService::isProtocolAllowed">isProtocolAllowed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#121"><abbr title="App\Services\SecurityValidationService::validateFileSize">validateFileSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#139"><abbr title="App\Services\SecurityValidationService::getSecurityConfig">getSecurityConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#152"><abbr title="App\Services\SecurityValidationService::isPrivateIp">isPrivateIp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#17"><abbr title="App\Services\StatisticsService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#25"><abbr title="App\Services\StatisticsService::recordValidation">recordValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#44"><abbr title="App\Services\StatisticsService::recordFileProcessed">recordFileProcessed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#59"><abbr title="App\Services\StatisticsService::getStatistics">getStatistics</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#71"><abbr title="App\Services\StatisticsService::getBrokenLinks">getBrokenLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#79"><abbr title="App\Services\StatisticsService::getProcessedFiles">getProcessedFiles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#87"><abbr title="App\Services\StatisticsService::reset">reset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#105"><abbr title="App\Services\StatisticsService::getSuccessRate">getSuccessRate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#16"><abbr title="App\Services\ValueObjects\ValidationConfig::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#33"><abbr title="App\Services\ValueObjects\ValidationConfig::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#51"><abbr title="App\Services\ValueObjects\ValidationConfig::withDefaults">withDefaults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#61"><abbr title="App\Services\ValueObjects\ValidationConfig::getScopes">getScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#69"><abbr title="App\Services\ValueObjects\ValidationConfig::hasScope">hasScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#80"><abbr title="App\Services\ValueObjects\ValidationConfig::getEffectiveScopes">getEffectiveScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#89"><abbr title="App\Services\ValueObjects\ValidationConfig::getTimeout">getTimeout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#94"><abbr title="App\Services\ValueObjects\ValidationConfig::getMaxRedirects">getMaxRedirects</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#99"><abbr title="App\Services\ValueObjects\ValidationConfig::getConcurrentRequests">getConcurrentRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#104"><abbr title="App\Services\ValueObjects\ValidationConfig::isCacheEnabled">isCacheEnabled</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#109"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldFollowRedirects">shouldFollowRedirects</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#114"><abbr title="App\Services\ValueObjects\ValidationConfig::getUserAgent">getUserAgent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#119"><abbr title="App\Services\ValueObjects\ValidationConfig::getOutputFormat">getOutputFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#124"><abbr title="App\Services\ValueObjects\ValidationConfig::getAdditionalOptions">getAdditionalOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#129"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateInternal">shouldValidateInternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#134"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateExternal">shouldValidateExternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#139"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateAnchors">shouldValidateAnchors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#144"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateCrossReferences">shouldValidateCrossReferences</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#154"><abbr title="App\Services\ValueObjects\ValidationConfig::withScopes">withScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#172"><abbr title="App\Services\ValueObjects\ValidationConfig::withOutputFormat">withOutputFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#187"><abbr title="App\Services\ValueObjects\ValidationConfig::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#202"><abbr title="App\Services\ValueObjects\ValidationConfig::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#12"><abbr title="App\Services\ValueObjects\ValidationResult::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#26"><abbr title="App\Services\ValueObjects\ValidationResult::success">success</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#46"><abbr title="App\Services\ValueObjects\ValidationResult::failure">failure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#68"><abbr title="App\Services\ValueObjects\ValidationResult::getUrl">getUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#73"><abbr title="App\Services\ValueObjects\ValidationResult::getStatus">getStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#78"><abbr title="App\Services\ValueObjects\ValidationResult::getScope">getScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#83"><abbr title="App\Services\ValueObjects\ValidationResult::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#88"><abbr title="App\Services\ValueObjects\ValidationResult::isBroken">isBroken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#93"><abbr title="App\Services\ValueObjects\ValidationResult::isTemporary">isTemporary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#98"><abbr title="App\Services\ValueObjects\ValidationResult::isSecurityIssue">isSecurityIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#103"><abbr title="App\Services\ValueObjects\ValidationResult::getError">getError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#108"><abbr title="App\Services\ValueObjects\ValidationResult::getHttpStatusCode">getHttpStatusCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#113"><abbr title="App\Services\ValueObjects\ValidationResult::getRedirectUrl">getRedirectUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#118"><abbr title="App\Services\ValueObjects\ValidationResult::getResponseTime">getResponseTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#123"><abbr title="App\Services\ValueObjects\ValidationResult::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#131"><abbr title="App\Services\ValueObjects\ValidationResult::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#139"><abbr title="App\Services\ValueObjects\ValidationResult::getRecommendedAction">getRecommendedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#147"><abbr title="App\Services\ValueObjects\ValidationResult::getConsoleColor">getConsoleColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#155"><abbr title="App\Services\ValueObjects\ValidationResult::getFormattedDisplay">getFormattedDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#163"><abbr title="App\Services\ValueObjects\ValidationResult::shouldRetry">shouldRetry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#168"><abbr title="App\Services\ValueObjects\ValidationResult::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationResult.php.html#189"><abbr title="App\Services\ValueObjects\ValidationResult::toJson">toJson</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#23"><abbr title="App\Enums\LinkStatus::values">values</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#31"><abbr title="App\Enums\LinkStatus::isBroken">isBroken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#39"><abbr title="App\Enums\LinkStatus::isTemporary">isTemporary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#50"><abbr title="App\Enums\LinkStatus::isSecurityIssue">isSecurityIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#61"><abbr title="App\Enums\LinkStatus::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#80"><abbr title="App\Enums\LinkStatus::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#94"><abbr title="App\Enums\LinkStatus::getRecommendedAction">getRecommendedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#113"><abbr title="App\Enums\LinkStatus::getConsoleColor">getConsoleColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#127"><abbr title="App\Enums\LinkStatus::getIcon">getIcon</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#146"><abbr title="App\Enums\LinkStatus::getGroup">getGroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#161"><abbr title="App\Enums\LinkStatus::getFormattedDisplay">getFormattedDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#173"><abbr title="App\Enums\LinkStatus::shouldRetry">shouldRetry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#184"><abbr title="App\Enums\LinkStatus::getHttpStatusCode">getHttpStatusCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#19"><abbr title="App\Enums\OutputFormat::values">values</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#27"><abbr title="App\Enums\OutputFormat::getSelectOptions">getSelectOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#41"><abbr title="App\Enums\OutputFormat::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#49"><abbr title="App\Enums\OutputFormat::getExtension">getExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#64"><abbr title="App\Enums\OutputFormat::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#79"><abbr title="App\Enums\OutputFormat::isStructured">isStructured</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#90"><abbr title="App\Enums\OutputFormat::supportsFormatting">supportsFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#101"><abbr title="App\Enums\OutputFormat::getFormatterClass">getFormatterClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#116"><abbr title="App\Enums\OutputFormat::getDefaultFilename">getDefaultFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#131"><abbr title="App\Enums\OutputFormat::isCiCdFriendly">isCiCdFriendly</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#142"><abbr title="App\Enums\OutputFormat::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#157"><abbr title="App\Enums\OutputFormat::getHelpText">getHelpText</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#172"><abbr title="App\Enums\OutputFormat::getUseCases">getUseCases</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#20"><abbr title="App\Enums\ValidationScope::values">values</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#28"><abbr title="App\Enums\ValidationScope::names">names</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#36"><abbr title="App\Enums\ValidationScope::getSelectOptions">getSelectOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#50"><abbr title="App\Enums\ValidationScope::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#58"><abbr title="App\Enums\ValidationScope::fromString">fromString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#66"><abbr title="App\Enums\ValidationScope::includesExternal">includesExternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#74"><abbr title="App\Enums\ValidationScope::includesInternal">includesInternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#82"><abbr title="App\Enums\ValidationScope::includesAnchor">includesAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#90"><abbr title="App\Enums\ValidationScope::includesImage">includesImage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#98"><abbr title="App\Enums\ValidationScope::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#112"><abbr title="App\Enums\ValidationScope::getHelpText">getHelpText</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#126"><abbr title="App\Enums\ValidationScope::getIncludedScopes">getIncludedScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#137"><abbr title="App\Enums\ValidationScope::getPriority">getPriority</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Commands/ValidateCommand.php.html#259"><abbr title="App\Commands\ValidateCommand::gatherAdvancedOptions">gatherAdvancedOptions</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#61"><abbr title="App\Enums\LinkStatus::getDescription">getDescription</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#94"><abbr title="App\Enums\LinkStatus::getRecommendedAction">getRecommendedAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#127"><abbr title="App\Enums\LinkStatus::getIcon">getIcon</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#202"><abbr title="App\Services\ValueObjects\ValidationConfig::validate">validate</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Services/Formatters/HtmlFormatter.php.html#19"><abbr title="App\Services\Formatters\HtmlFormatter::generateContent">generateContent</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#39"><abbr title="App\Services\SecurityValidationService::validateUrl">validateUrl</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#20"><abbr title="App\Services\LinkValidationService::validateFile">validateFile</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#184"><abbr title="App\Enums\LinkStatus::getHttpStatusCode">getHttpStatusCode</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#468"><abbr title="App\Commands\ValidateCommand::performValidation">performValidation</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Services/GitHubAnchorService.php.html#48"><abbr title="App\Services\GitHubAnchorService::extractAnchors">extractAnchors</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#146"><abbr title="App\Enums\LinkStatus::getGroup">getGroup</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#49"><abbr title="App\Enums\OutputFormat::getExtension">getExtension</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#64"><abbr title="App\Enums\OutputFormat::getMimeType">getMimeType</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#101"><abbr title="App\Enums\OutputFormat::getFormatterClass">getFormatterClass</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#116"><abbr title="App\Enums\OutputFormat::getDefaultFilename">getDefaultFilename</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#142"><abbr title="App\Enums\OutputFormat::getDescription">getDescription</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#157"><abbr title="App\Enums\OutputFormat::getHelpText">getHelpText</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#172"><abbr title="App\Enums\OutputFormat::getUseCases">getUseCases</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#326"><abbr title="App\Commands\ValidateCommand::executeInteractiveValidation">executeInteractiveValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/Formatters/MarkdownFormatter.php.html#11"><abbr title="App\Services\Formatters\MarkdownFormatter::format">format</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#87"><abbr title="App\Services\ReportingService::generateSummary">generateSummary</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#74"><abbr title="App\Services\SecurityValidationService::validatePath">validatePath</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#80"><abbr title="App\Enums\LinkStatus::getSeverity">getSeverity</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#113"><abbr title="App\Enums\LinkStatus::getConsoleColor">getConsoleColor</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#98"><abbr title="App\Enums\ValidationScope::getDescription">getDescription</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#112"><abbr title="App\Enums\ValidationScope::getHelpText">getHelpText</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#137"><abbr title="App\Enums\ValidationScope::getPriority">getPriority</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#127"><abbr title="App\Commands\BaseValidationCommand::displayResults">displayResults</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#212"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroup">displayStatusGroup</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#186"><abbr title="App\Commands\ValidateCommand::gatherOutputConfiguration">gatherOutputConfiguration</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#396"><abbr title="App\Commands\ValidateCommand::getFilesFromPath">getFilesFromPath</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#78"><abbr title="App\Services\LinkValidationService::categorizeLinks">categorizeLinks</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#78"><abbr title="App\Commands\BaseValidationCommand::validateCommandOptions">validateCommandOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#36"><abbr title="App\Commands\ConfigCommand::initializeConfig">initializeConfig</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#239"><abbr title="App\Commands\ValidateCommand::validateOutputPath">validateOutputPath</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#379"><abbr title="App\Commands\ValidateCommand::countFiles">countFiles</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#419"><abbr title="App\Commands\ValidateCommand::hasBrokenLinks">hasBrokenLinks</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Exceptions/Handler.php.html#23"><abbr title="App\Exceptions\Handler::renderValidateLinksException">renderValidateLinksException</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#71"><abbr title="App\Services\Formatters\ConsoleFormatter::formatStatistics">formatStatistics</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#102"><abbr title="App\Services\LinkValidationService::validateCrossReferences">validateCrossReferences</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#133"><abbr title="App\Services\LinkValidationService::validateCrossReference">validateCrossReference</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#170"><abbr title="App\Commands\BaseValidationCommand::isValidScopeList">isValidScopeList</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/ConfigCommand.php.html#21"><abbr title="App\Commands\ConfigCommand::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#23"><abbr title="App\Commands\FixCommand::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#61"><abbr title="App\Commands\ValidateCommand::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#111"><abbr title="App\Commands\ValidateCommand::gatherPaths">gatherPaths</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#138"><abbr title="App\Commands\ValidateCommand::gatherValidationScope">gatherValidationScope</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#170"><abbr title="App\Commands\ValidateCommand::getScopeOptions">getScopeOptions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#306"><abbr title="App\Commands\ValidateCommand::confirmConfiguration">confirmConfiguration</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#35"><abbr title="App\Services\Formatters\ConsoleFormatter::formatHeader">formatHeader</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#51"><abbr title="App\Services\LinkValidationService::extractLinks">extractLinks</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#60"><abbr title="App\Services\ReportingService::exportReport">exportReport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#121"><abbr title="App\Services\SecurityValidationService::validateFileSize">validateFileSize</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/SecurityValidationService.php.html#152"><abbr title="App\Services\SecurityValidationService::isPrivateIp">isPrivateIp</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#39"><abbr title="App\Enums\LinkStatus::isTemporary">isTemporary</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#50"><abbr title="App\Enums\LinkStatus::isSecurityIssue">isSecurityIssue</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Enums/LinkStatus.php.html#173"><abbr title="App\Enums\LinkStatus::shouldRetry">shouldRetry</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#79"><abbr title="App\Enums\OutputFormat::isStructured">isStructured</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#90"><abbr title="App\Enums\OutputFormat::supportsFormatting">supportsFormatting</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Enums/OutputFormat.php.html#131"><abbr title="App\Enums\OutputFormat::isCiCdFriendly">isCiCdFriendly</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#126"><abbr title="App\Enums\ValidationScope::getIncludedScopes">getIncludedScopes</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#53"><abbr title="App\Commands\BaseValidationCommand::parseScopes">parseScopes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#101"><abbr title="App\Commands\BaseValidationCommand::processValidationResults">processValidationResults</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#156"><abbr title="App\Commands\BaseValidationCommand::handleValidationError">handleValidationError</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#186"><abbr title="App\Commands\BaseValidationCommand::groupLinksByStatus">groupLinksByStatus</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/BaseValidationCommand.php.html#201"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroups">displayStatusGroups</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#46"><abbr title="App\Commands\FixCommand::handleInteractiveFix">handleInteractiveFix</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/FixCommand.php.html#64"><abbr title="App\Commands\FixCommand::handleAutomaticFix">handleAutomaticFix</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/ReportCommand.php.html#21"><abbr title="App\Commands\ReportCommand::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#80"><abbr title="App\Commands\ValidateCommand::handleInteractive">handleInteractive</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Commands/ValidateCommand.php.html#433"><abbr title="App\Commands\ValidateCommand::displaySummary">displaySummary</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Exceptions/Handler.php.html#14"><abbr title="App\Exceptions\Handler::render">render</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/ConcurrentValidationService.php.html#52"><abbr title="App\Services\ConcurrentValidationService::createRequests">createRequests</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#14"><abbr title="App\Services\Formatters\ConsoleFormatter::format">format</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Formatters/ConsoleFormatter.php.html#57"><abbr title="App\Services\Formatters\ConsoleFormatter::formatBrokenLinks">formatBrokenLinks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Formatters/JsonFormatter.php.html#14"><abbr title="App\Services\Formatters\JsonFormatter::format">format</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/LinkValidationService.php.html#120"><abbr title="App\Services\LinkValidationService::extractAllAnchors">extractAllAnchors</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/ReportingService.php.html#37"><abbr title="App\Services\ReportingService::generateReport">generateReport</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#25"><abbr title="App\Services\StatisticsService::recordValidation">recordValidation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/StatisticsService.php.html#105"><abbr title="App\Services\StatisticsService::getSuccessRate">getSuccessRate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#69"><abbr title="App\Services\ValueObjects\ValidationConfig::hasScope">hasScope</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/ValueObjects/ValidationConfig.php.html#80"><abbr title="App\Services\ValueObjects\ValidationConfig::getEffectiveScopes">getEffectiveScopes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#66"><abbr title="App\Enums\ValidationScope::includesExternal">includesExternal</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#74"><abbr title="App\Enums\ValidationScope::includesInternal">includesInternal</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#82"><abbr title="App\Enums\ValidationScope::includesAnchor">includesAnchor</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Enums/ValidationScope.php.html#90"><abbr title="App\Enums\ValidationScope::includesImage">includesImage</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.10</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.10</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Fri Jul 25 13:37:46 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="_js/d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="_js/nv.d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([29,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([190,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,31,"<a href=\"Commands\/BaseValidationCommand.php.html#15\">App\\Commands\\BaseValidationCommand<\/a>"],[0,9,"<a href=\"Commands\/ConfigCommand.php.html#13\">App\\Commands\\ConfigCommand<\/a>"],[0,7,"<a href=\"Commands\/FixCommand.php.html#13\">App\\Commands\\FixCommand<\/a>"],[0,2,"<a href=\"Commands\/InspireCommand.php.html#12\">App\\Commands\\InspireCommand<\/a>"],[0,2,"<a href=\"Commands\/ReportCommand.php.html#11\">App\\Commands\\ReportCommand<\/a>"],[0,68,"<a href=\"Commands\/ValidateCommand.php.html#21\">App\\Commands\\ValidateCommand<\/a>"],[0,2,"<a href=\"Exceptions\/ConfigurationException.php.html#10\">App\\Exceptions\\ConfigurationException<\/a>"],[0,6,"<a href=\"Exceptions\/Handler.php.html#12\">App\\Exceptions\\Handler<\/a>"],[0,2,"<a href=\"Exceptions\/SecurityException.php.html#10\">App\\Exceptions\\SecurityException<\/a>"],[0,2,"<a href=\"Exceptions\/ValidateLinksException.php.html#12\">App\\Exceptions\\ValidateLinksException<\/a>"],[0,2,"<a href=\"Exceptions\/ValidationException.php.html#10\">App\\Exceptions\\ValidationException<\/a>"],[0,2,"<a href=\"Providers\/AppServiceProvider.php.html#9\">App\\Providers\\AppServiceProvider<\/a>"],[0,3,"<a href=\"Providers\/ValidateLinksServiceProvider.php.html#19\">App\\Providers\\ValidateLinksServiceProvider<\/a>"],[0,4,"<a href=\"Services\/ConcurrentValidationService.php.html#14\">App\\Services\\ConcurrentValidationService<\/a>"],[0,12,"<a href=\"Services\/Formatters\/ConsoleFormatter.php.html#9\">App\\Services\\Formatters\\ConsoleFormatter<\/a>"],[0,11,"<a href=\"Services\/Formatters\/HtmlFormatter.php.html#9\">App\\Services\\Formatters\\HtmlFormatter<\/a>"],[0,2,"<a href=\"Services\/Formatters\/JsonFormatter.php.html#9\">App\\Services\\Formatters\\JsonFormatter<\/a>"],[0,6,"<a href=\"Services\/Formatters\/MarkdownFormatter.php.html#9\">App\\Services\\Formatters\\MarkdownFormatter<\/a>"],[0,10,"<a href=\"Services\/GitHubAnchorService.php.html#9\">App\\Services\\GitHubAnchorService<\/a>"],[0,27,"<a href=\"Services\/LinkValidationService.php.html#12\">App\\Services\\LinkValidationService<\/a>"],[0,4,"<a href=\"Services\/PluginManager.php.html#12\">App\\Services\\PluginManager<\/a>"],[0,14,"<a href=\"Services\/ReportingService.php.html#17\">App\\Services\\ReportingService<\/a>"],[0,25,"<a href=\"Services\/SecurityValidationService.php.html#10\">App\\Services\\SecurityValidationService<\/a>"],[0,10,"<a href=\"Services\/StatisticsService.php.html#9\">App\\Services\\StatisticsService<\/a>"],[0,33,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#11\">App\\Services\\ValueObjects\\ValidationConfig<\/a>"],[0,22,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#10\">App\\Services\\ValueObjects\\ValidationResult<\/a>"],[0,72,"<a href=\"Enums\/LinkStatus.php.html#7\">App\\Enums\\LinkStatus<\/a>"],[0,61,"<a href=\"Enums\/OutputFormat.php.html#7\">App\\Enums\\OutputFormat<\/a>"],[0,34,"<a href=\"Enums\/ValidationScope.php.html#9\">App\\Enums\\ValidationScope<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Commands\/BaseValidationCommand.php.html#21\">App\\Commands\\BaseValidationCommand::__construct<\/a>"],[0,1,"<a href=\"Commands\/BaseValidationCommand.php.html#34\">App\\Commands\\BaseValidationCommand::createConfigFromOptions<\/a>"],[0,2,"<a href=\"Commands\/BaseValidationCommand.php.html#53\">App\\Commands\\BaseValidationCommand::parseScopes<\/a>"],[0,1,"<a href=\"Commands\/BaseValidationCommand.php.html#70\">App\\Commands\\BaseValidationCommand::getOutputFormat<\/a>"],[0,4,"<a href=\"Commands\/BaseValidationCommand.php.html#78\">App\\Commands\\BaseValidationCommand::validateCommandOptions<\/a>"],[0,2,"<a href=\"Commands\/BaseValidationCommand.php.html#101\">App\\Commands\\BaseValidationCommand::processValidationResults<\/a>"],[0,5,"<a href=\"Commands\/BaseValidationCommand.php.html#127\">App\\Commands\\BaseValidationCommand::displayResults<\/a>"],[0,2,"<a href=\"Commands\/BaseValidationCommand.php.html#156\">App\\Commands\\BaseValidationCommand::handleValidationError<\/a>"],[0,3,"<a href=\"Commands\/BaseValidationCommand.php.html#170\">App\\Commands\\BaseValidationCommand::isValidScopeList<\/a>"],[0,2,"<a href=\"Commands\/BaseValidationCommand.php.html#186\">App\\Commands\\BaseValidationCommand::groupLinksByStatus<\/a>"],[0,2,"<a href=\"Commands\/BaseValidationCommand.php.html#201\">App\\Commands\\BaseValidationCommand::displayStatusGroups<\/a>"],[0,5,"<a href=\"Commands\/BaseValidationCommand.php.html#212\">App\\Commands\\BaseValidationCommand::displayStatusGroup<\/a>"],[0,1,"<a href=\"Commands\/BaseValidationCommand.php.html#234\">App\\Commands\\BaseValidationCommand::displayValidationSummary<\/a>"],[0,3,"<a href=\"Commands\/ConfigCommand.php.html#21\">App\\Commands\\ConfigCommand::handle<\/a>"],[0,4,"<a href=\"Commands\/ConfigCommand.php.html#36\">App\\Commands\\ConfigCommand::initializeConfig<\/a>"],[0,1,"<a href=\"Commands\/ConfigCommand.php.html#63\">App\\Commands\\ConfigCommand::showConfig<\/a>"],[0,1,"<a href=\"Commands\/ConfigCommand.php.html#74\">App\\Commands\\ConfigCommand::gatherConfigurationSettings<\/a>"],[0,3,"<a href=\"Commands\/FixCommand.php.html#23\">App\\Commands\\FixCommand::handle<\/a>"],[0,2,"<a href=\"Commands\/FixCommand.php.html#46\">App\\Commands\\FixCommand::handleInteractiveFix<\/a>"],[0,2,"<a href=\"Commands\/FixCommand.php.html#64\">App\\Commands\\FixCommand::handleAutomaticFix<\/a>"],[0,1,"<a href=\"Commands\/InspireCommand.php.html#31\">App\\Commands\\InspireCommand::handle<\/a>"],[0,1,"<a href=\"Commands\/InspireCommand.php.html#46\">App\\Commands\\InspireCommand::schedule<\/a>"],[0,2,"<a href=\"Commands\/ReportCommand.php.html#21\">App\\Commands\\ReportCommand::handle<\/a>"],[0,1,"<a href=\"Commands\/ValidateCommand.php.html#49\">App\\Commands\\ValidateCommand::__construct<\/a>"],[0,3,"<a href=\"Commands\/ValidateCommand.php.html#61\">App\\Commands\\ValidateCommand::handle<\/a>"],[0,2,"<a href=\"Commands\/ValidateCommand.php.html#80\">App\\Commands\\ValidateCommand::handleInteractive<\/a>"],[0,3,"<a href=\"Commands\/ValidateCommand.php.html#111\">App\\Commands\\ValidateCommand::gatherPaths<\/a>"],[0,3,"<a href=\"Commands\/ValidateCommand.php.html#138\">App\\Commands\\ValidateCommand::gatherValidationScope<\/a>"],[0,3,"<a href=\"Commands\/ValidateCommand.php.html#170\">App\\Commands\\ValidateCommand::getScopeOptions<\/a>"],[0,5,"<a href=\"Commands\/ValidateCommand.php.html#186\">App\\Commands\\ValidateCommand::gatherOutputConfiguration<\/a>"],[0,4,"<a href=\"Commands\/ValidateCommand.php.html#239\">App\\Commands\\ValidateCommand::validateOutputPath<\/a>"],[0,12,"<a href=\"Commands\/ValidateCommand.php.html#259\">App\\Commands\\ValidateCommand::gatherAdvancedOptions<\/a>"],[0,3,"<a href=\"Commands\/ValidateCommand.php.html#306\">App\\Commands\\ValidateCommand::confirmConfiguration<\/a>"],[0,6,"<a href=\"Commands\/ValidateCommand.php.html#326\">App\\Commands\\ValidateCommand::executeInteractiveValidation<\/a>"],[0,4,"<a href=\"Commands\/ValidateCommand.php.html#379\">App\\Commands\\ValidateCommand::countFiles<\/a>"],[0,5,"<a href=\"Commands\/ValidateCommand.php.html#396\">App\\Commands\\ValidateCommand::getFilesFromPath<\/a>"],[0,4,"<a href=\"Commands\/ValidateCommand.php.html#419\">App\\Commands\\ValidateCommand::hasBrokenLinks<\/a>"],[0,2,"<a href=\"Commands\/ValidateCommand.php.html#433\">App\\Commands\\ValidateCommand::displaySummary<\/a>"],[0,1,"<a href=\"Commands\/ValidateCommand.php.html#453\">App\\Commands\\ValidateCommand::handleNonInteractive<\/a>"],[0,7,"<a href=\"Commands\/ValidateCommand.php.html#468\">App\\Commands\\ValidateCommand::performValidation<\/a>"],[0,1,"<a href=\"Exceptions\/ConfigurationException.php.html#12\">App\\Exceptions\\ConfigurationException::getErrorCode<\/a>"],[0,1,"<a href=\"Exceptions\/ConfigurationException.php.html#17\">App\\Exceptions\\ConfigurationException::getSeverity<\/a>"],[0,2,"<a href=\"Exceptions\/Handler.php.html#14\">App\\Exceptions\\Handler::render<\/a>"],[0,4,"<a href=\"Exceptions\/Handler.php.html#23\">App\\Exceptions\\Handler::renderValidateLinksException<\/a>"],[0,1,"<a href=\"Exceptions\/SecurityException.php.html#12\">App\\Exceptions\\SecurityException::getErrorCode<\/a>"],[0,1,"<a href=\"Exceptions\/SecurityException.php.html#17\">App\\Exceptions\\SecurityException::getSeverity<\/a>"],[0,0,"<a href=\"Exceptions\/ValidateLinksException.php.html#17\">App\\Exceptions\\ValidateLinksException::getErrorCode<\/a>"],[0,0,"<a href=\"Exceptions\/ValidateLinksException.php.html#22\">App\\Exceptions\\ValidateLinksException::getSeverity<\/a>"],[0,1,"<a href=\"Exceptions\/ValidateLinksException.php.html#27\">App\\Exceptions\\ValidateLinksException::getContext<\/a>"],[0,1,"<a href=\"Exceptions\/ValidateLinksException.php.html#35\">App\\Exceptions\\ValidateLinksException::toArray<\/a>"],[0,1,"<a href=\"Exceptions\/ValidationException.php.html#12\">App\\Exceptions\\ValidationException::getErrorCode<\/a>"],[0,1,"<a href=\"Exceptions\/ValidationException.php.html#17\">App\\Exceptions\\ValidationException::getSeverity<\/a>"],[0,1,"<a href=\"Providers\/AppServiceProvider.php.html#14\">App\\Providers\\AppServiceProvider::boot<\/a>"],[0,1,"<a href=\"Providers\/AppServiceProvider.php.html#22\">App\\Providers\\AppServiceProvider::register<\/a>"],[0,1,"<a href=\"Providers\/ValidateLinksServiceProvider.php.html#27\">App\\Providers\\ValidateLinksServiceProvider::register<\/a>"],[0,1,"<a href=\"Providers\/ValidateLinksServiceProvider.php.html#62\">App\\Providers\\ValidateLinksServiceProvider::boot<\/a>"],[0,1,"<a href=\"Providers\/ValidateLinksServiceProvider.php.html#76\">App\\Providers\\ValidateLinksServiceProvider::provides<\/a>"],[0,1,"<a href=\"Services\/ConcurrentValidationService.php.html#20\">App\\Services\\ConcurrentValidationService::__construct<\/a>"],[0,1,"<a href=\"Services\/ConcurrentValidationService.php.html#31\">App\\Services\\ConcurrentValidationService::validateExternalLinksConcurrently<\/a>"],[0,2,"<a href=\"Services\/ConcurrentValidationService.php.html#52\">App\\Services\\ConcurrentValidationService::createRequests<\/a>"],[0,2,"<a href=\"Services\/Formatters\/ConsoleFormatter.php.html#14\">App\\Services\\Formatters\\ConsoleFormatter::format<\/a>"],[0,3,"<a href=\"Services\/Formatters\/ConsoleFormatter.php.html#35\">App\\Services\\Formatters\\ConsoleFormatter::formatHeader<\/a>"],[0,1,"<a href=\"Services\/Formatters\/ConsoleFormatter.php.html#43\">App\\Services\\Formatters\\ConsoleFormatter::formatSummary<\/a>"],[0,2,"<a href=\"Services\/Formatters\/ConsoleFormatter.php.html#57\">App\\Services\\Formatters\\ConsoleFormatter::formatBrokenLinks<\/a>"],[0,4,"<a href=\"Services\/Formatters\/ConsoleFormatter.php.html#71\">App\\Services\\Formatters\\ConsoleFormatter::formatStatistics<\/a>"],[0,1,"<a href=\"Services\/Formatters\/HtmlFormatter.php.html#11\">App\\Services\\Formatters\\HtmlFormatter::format<\/a>"],[0,9,"<a href=\"Services\/Formatters\/HtmlFormatter.php.html#19\">App\\Services\\Formatters\\HtmlFormatter::generateContent<\/a>"],[0,1,"<a href=\"Services\/Formatters\/HtmlFormatter.php.html#99\">App\\Services\\Formatters\\HtmlFormatter::getHtmlTemplate<\/a>"],[0,2,"<a href=\"Services\/Formatters\/JsonFormatter.php.html#14\">App\\Services\\Formatters\\JsonFormatter::format<\/a>"],[0,6,"<a href=\"Services\/Formatters\/MarkdownFormatter.php.html#11\">App\\Services\\Formatters\\MarkdownFormatter::format<\/a>"],[0,1,"<a href=\"Services\/GitHubAnchorService.php.html#14\">App\\Services\\GitHubAnchorService::generateAnchor<\/a>"],[0,1,"<a href=\"Services\/GitHubAnchorService.php.html#37\">App\\Services\\GitHubAnchorService::validateAnchor<\/a>"],[0,7,"<a href=\"Services\/GitHubAnchorService.php.html#48\">App\\Services\\GitHubAnchorService::extractAnchors<\/a>"],[0,1,"<a href=\"Services\/GitHubAnchorService.php.html#79\">App\\Services\\GitHubAnchorService::normalizeAnchor<\/a>"],[0,1,"<a href=\"Services\/LinkValidationService.php.html#14\">App\\Services\\LinkValidationService::__construct<\/a>"],[0,8,"<a href=\"Services\/LinkValidationService.php.html#20\">App\\Services\\LinkValidationService::validateFile<\/a>"],[0,3,"<a href=\"Services\/LinkValidationService.php.html#51\">App\\Services\\LinkValidationService::extractLinks<\/a>"],[0,5,"<a href=\"Services\/LinkValidationService.php.html#78\">App\\Services\\LinkValidationService::categorizeLinks<\/a>"],[0,4,"<a href=\"Services\/LinkValidationService.php.html#102\">App\\Services\\LinkValidationService::validateCrossReferences<\/a>"],[0,2,"<a href=\"Services\/LinkValidationService.php.html#120\">App\\Services\\LinkValidationService::extractAllAnchors<\/a>"],[0,4,"<a href=\"Services\/LinkValidationService.php.html#133\">App\\Services\\LinkValidationService::validateCrossReference<\/a>"],[0,1,"<a href=\"Services\/PluginManager.php.html#16\">App\\Services\\PluginManager::__construct<\/a>"],[0,1,"<a href=\"Services\/PluginManager.php.html#21\">App\\Services\\PluginManager::register<\/a>"],[0,1,"<a href=\"Services\/PluginManager.php.html#27\">App\\Services\\PluginManager::boot<\/a>"],[0,1,"<a href=\"Services\/PluginManager.php.html#34\">App\\Services\\PluginManager::getPlugin<\/a>"],[0,1,"<a href=\"Services\/ReportingService.php.html#23\">App\\Services\\ReportingService::__construct<\/a>"],[0,2,"<a href=\"Services\/ReportingService.php.html#37\">App\\Services\\ReportingService::generateReport<\/a>"],[0,1,"<a href=\"Services\/ReportingService.php.html#52\">App\\Services\\ReportingService::addFormatter<\/a>"],[0,3,"<a href=\"Services\/ReportingService.php.html#60\">App\\Services\\ReportingService::exportReport<\/a>"],[0,1,"<a href=\"Services\/ReportingService.php.html#79\">App\\Services\\ReportingService::getAvailableFormats<\/a>"],[0,6,"<a href=\"Services\/ReportingService.php.html#87\">App\\Services\\ReportingService::generateSummary<\/a>"],[0,1,"<a href=\"Services\/SecurityValidationService.php.html#20\">App\\Services\\SecurityValidationService::__construct<\/a>"],[0,9,"<a href=\"Services\/SecurityValidationService.php.html#39\">App\\Services\\SecurityValidationService::validateUrl<\/a>"],[0,6,"<a href=\"Services\/SecurityValidationService.php.html#74\">App\\Services\\SecurityValidationService::validatePath<\/a>"],[0,1,"<a href=\"Services\/SecurityValidationService.php.html#105\">App\\Services\\SecurityValidationService::isDomainBlocked<\/a>"],[0,1,"<a href=\"Services\/SecurityValidationService.php.html#113\">App\\Services\\SecurityValidationService::isProtocolAllowed<\/a>"],[0,3,"<a href=\"Services\/SecurityValidationService.php.html#121\">App\\Services\\SecurityValidationService::validateFileSize<\/a>"],[0,1,"<a href=\"Services\/SecurityValidationService.php.html#139\">App\\Services\\SecurityValidationService::getSecurityConfig<\/a>"],[0,3,"<a href=\"Services\/SecurityValidationService.php.html#152\">App\\Services\\SecurityValidationService::isPrivateIp<\/a>"],[0,1,"<a href=\"Services\/StatisticsService.php.html#17\">App\\Services\\StatisticsService::__construct<\/a>"],[0,2,"<a href=\"Services\/StatisticsService.php.html#25\">App\\Services\\StatisticsService::recordValidation<\/a>"],[0,1,"<a href=\"Services\/StatisticsService.php.html#44\">App\\Services\\StatisticsService::recordFileProcessed<\/a>"],[0,1,"<a href=\"Services\/StatisticsService.php.html#59\">App\\Services\\StatisticsService::getStatistics<\/a>"],[0,1,"<a href=\"Services\/StatisticsService.php.html#71\">App\\Services\\StatisticsService::getBrokenLinks<\/a>"],[0,1,"<a href=\"Services\/StatisticsService.php.html#79\">App\\Services\\StatisticsService::getProcessedFiles<\/a>"],[0,1,"<a href=\"Services\/StatisticsService.php.html#87\">App\\Services\\StatisticsService::reset<\/a>"],[0,2,"<a href=\"Services\/StatisticsService.php.html#105\">App\\Services\\StatisticsService::getSuccessRate<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#16\">App\\Services\\ValueObjects\\ValidationConfig::__construct<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#33\">App\\Services\\ValueObjects\\ValidationConfig::create<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#51\">App\\Services\\ValueObjects\\ValidationConfig::withDefaults<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#61\">App\\Services\\ValueObjects\\ValidationConfig::getScopes<\/a>"],[0,2,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#69\">App\\Services\\ValueObjects\\ValidationConfig::hasScope<\/a>"],[0,2,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#80\">App\\Services\\ValueObjects\\ValidationConfig::getEffectiveScopes<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#89\">App\\Services\\ValueObjects\\ValidationConfig::getTimeout<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#94\">App\\Services\\ValueObjects\\ValidationConfig::getMaxRedirects<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#99\">App\\Services\\ValueObjects\\ValidationConfig::getConcurrentRequests<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#104\">App\\Services\\ValueObjects\\ValidationConfig::isCacheEnabled<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#109\">App\\Services\\ValueObjects\\ValidationConfig::shouldFollowRedirects<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#114\">App\\Services\\ValueObjects\\ValidationConfig::getUserAgent<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#119\">App\\Services\\ValueObjects\\ValidationConfig::getOutputFormat<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#124\">App\\Services\\ValueObjects\\ValidationConfig::getAdditionalOptions<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#129\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateInternal<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#134\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateExternal<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#139\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateAnchors<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#144\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateCrossReferences<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#154\">App\\Services\\ValueObjects\\ValidationConfig::withScopes<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#172\">App\\Services\\ValueObjects\\ValidationConfig::withOutputFormat<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#187\">App\\Services\\ValueObjects\\ValidationConfig::toArray<\/a>"],[0,10,"<a href=\"Services\/ValueObjects\/ValidationConfig.php.html#202\">App\\Services\\ValueObjects\\ValidationConfig::validate<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#12\">App\\Services\\ValueObjects\\ValidationResult::__construct<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#26\">App\\Services\\ValueObjects\\ValidationResult::success<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#46\">App\\Services\\ValueObjects\\ValidationResult::failure<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#68\">App\\Services\\ValueObjects\\ValidationResult::getUrl<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#73\">App\\Services\\ValueObjects\\ValidationResult::getStatus<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#78\">App\\Services\\ValueObjects\\ValidationResult::getScope<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#83\">App\\Services\\ValueObjects\\ValidationResult::isValid<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#88\">App\\Services\\ValueObjects\\ValidationResult::isBroken<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#93\">App\\Services\\ValueObjects\\ValidationResult::isTemporary<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#98\">App\\Services\\ValueObjects\\ValidationResult::isSecurityIssue<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#103\">App\\Services\\ValueObjects\\ValidationResult::getError<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#108\">App\\Services\\ValueObjects\\ValidationResult::getHttpStatusCode<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#113\">App\\Services\\ValueObjects\\ValidationResult::getRedirectUrl<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#118\">App\\Services\\ValueObjects\\ValidationResult::getResponseTime<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#123\">App\\Services\\ValueObjects\\ValidationResult::getMetadata<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#131\">App\\Services\\ValueObjects\\ValidationResult::getSeverity<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#139\">App\\Services\\ValueObjects\\ValidationResult::getRecommendedAction<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#147\">App\\Services\\ValueObjects\\ValidationResult::getConsoleColor<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#155\">App\\Services\\ValueObjects\\ValidationResult::getFormattedDisplay<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#163\">App\\Services\\ValueObjects\\ValidationResult::shouldRetry<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#168\">App\\Services\\ValueObjects\\ValidationResult::toArray<\/a>"],[0,1,"<a href=\"Services\/ValueObjects\/ValidationResult.php.html#189\">App\\Services\\ValueObjects\\ValidationResult::toJson<\/a>"],[0,1,"<a href=\"Enums\/LinkStatus.php.html#23\">App\\Enums\\LinkStatus::values<\/a>"],[0,1,"<a href=\"Enums\/LinkStatus.php.html#31\">App\\Enums\\LinkStatus::isBroken<\/a>"],[0,3,"<a href=\"Enums\/LinkStatus.php.html#39\">App\\Enums\\LinkStatus::isTemporary<\/a>"],[0,3,"<a href=\"Enums\/LinkStatus.php.html#50\">App\\Enums\\LinkStatus::isSecurityIssue<\/a>"],[0,11,"<a href=\"Enums\/LinkStatus.php.html#61\">App\\Enums\\LinkStatus::getDescription<\/a>"],[0,6,"<a href=\"Enums\/LinkStatus.php.html#80\">App\\Enums\\LinkStatus::getSeverity<\/a>"],[0,11,"<a href=\"Enums\/LinkStatus.php.html#94\">App\\Enums\\LinkStatus::getRecommendedAction<\/a>"],[0,6,"<a href=\"Enums\/LinkStatus.php.html#113\">App\\Enums\\LinkStatus::getConsoleColor<\/a>"],[0,11,"<a href=\"Enums\/LinkStatus.php.html#127\">App\\Enums\\LinkStatus::getIcon<\/a>"],[0,7,"<a href=\"Enums\/LinkStatus.php.html#146\">App\\Enums\\LinkStatus::getGroup<\/a>"],[0,1,"<a href=\"Enums\/LinkStatus.php.html#161\">App\\Enums\\LinkStatus::getFormattedDisplay<\/a>"],[0,3,"<a href=\"Enums\/LinkStatus.php.html#173\">App\\Enums\\LinkStatus::shouldRetry<\/a>"],[0,8,"<a href=\"Enums\/LinkStatus.php.html#184\">App\\Enums\\LinkStatus::getHttpStatusCode<\/a>"],[0,1,"<a href=\"Enums\/OutputFormat.php.html#19\">App\\Enums\\OutputFormat::values<\/a>"],[0,1,"<a href=\"Enums\/OutputFormat.php.html#27\">App\\Enums\\OutputFormat::getSelectOptions<\/a>"],[0,1,"<a href=\"Enums\/OutputFormat.php.html#41\">App\\Enums\\OutputFormat::isValid<\/a>"],[0,7,"<a href=\"Enums\/OutputFormat.php.html#49\">App\\Enums\\OutputFormat::getExtension<\/a>"],[0,7,"<a href=\"Enums\/OutputFormat.php.html#64\">App\\Enums\\OutputFormat::getMimeType<\/a>"],[0,3,"<a href=\"Enums\/OutputFormat.php.html#79\">App\\Enums\\OutputFormat::isStructured<\/a>"],[0,3,"<a href=\"Enums\/OutputFormat.php.html#90\">App\\Enums\\OutputFormat::supportsFormatting<\/a>"],[0,7,"<a href=\"Enums\/OutputFormat.php.html#101\">App\\Enums\\OutputFormat::getFormatterClass<\/a>"],[0,7,"<a href=\"Enums\/OutputFormat.php.html#116\">App\\Enums\\OutputFormat::getDefaultFilename<\/a>"],[0,3,"<a href=\"Enums\/OutputFormat.php.html#131\">App\\Enums\\OutputFormat::isCiCdFriendly<\/a>"],[0,7,"<a href=\"Enums\/OutputFormat.php.html#142\">App\\Enums\\OutputFormat::getDescription<\/a>"],[0,7,"<a href=\"Enums\/OutputFormat.php.html#157\">App\\Enums\\OutputFormat::getHelpText<\/a>"],[0,7,"<a href=\"Enums\/OutputFormat.php.html#172\">App\\Enums\\OutputFormat::getUseCases<\/a>"],[0,1,"<a href=\"Enums\/ValidationScope.php.html#20\">App\\Enums\\ValidationScope::values<\/a>"],[0,1,"<a href=\"Enums\/ValidationScope.php.html#28\">App\\Enums\\ValidationScope::names<\/a>"],[0,1,"<a href=\"Enums\/ValidationScope.php.html#36\">App\\Enums\\ValidationScope::getSelectOptions<\/a>"],[0,1,"<a href=\"Enums\/ValidationScope.php.html#50\">App\\Enums\\ValidationScope::isValid<\/a>"],[0,1,"<a href=\"Enums\/ValidationScope.php.html#58\">App\\Enums\\ValidationScope::fromString<\/a>"],[0,2,"<a href=\"Enums\/ValidationScope.php.html#66\">App\\Enums\\ValidationScope::includesExternal<\/a>"],[0,2,"<a href=\"Enums\/ValidationScope.php.html#74\">App\\Enums\\ValidationScope::includesInternal<\/a>"],[0,2,"<a href=\"Enums\/ValidationScope.php.html#82\">App\\Enums\\ValidationScope::includesAnchor<\/a>"],[0,2,"<a href=\"Enums\/ValidationScope.php.html#90\">App\\Enums\\ValidationScope::includesImage<\/a>"],[0,6,"<a href=\"Enums\/ValidationScope.php.html#98\">App\\Enums\\ValidationScope::getDescription<\/a>"],[0,6,"<a href=\"Enums\/ValidationScope.php.html#112\">App\\Enums\\ValidationScope::getHelpText<\/a>"],[0,3,"<a href=\"Enums\/ValidationScope.php.html#126\">App\\Enums\\ValidationScope::getIncludedScopes<\/a>"],[0,6,"<a href=\"Enums\/ValidationScope.php.html#137\">App\\Enums\\ValidationScope::getPriority<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
