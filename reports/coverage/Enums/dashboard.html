<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Herd/validate-links/app/Enums</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/Users/<USER>/Herd/validate-links/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Enums</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LinkStatus.php.html#7">App\Enums\LinkStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#7">App\Enums\OutputFormat</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#9">App\Enums\ValidationScope</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LinkStatus.php.html#7">App\Enums\LinkStatus</a></td><td class="text-right">5256</td></tr>
       <tr><td><a href="OutputFormat.php.html#7">App\Enums\OutputFormat</a></td><td class="text-right">3782</td></tr>
       <tr><td><a href="ValidationScope.php.html#9">App\Enums\ValidationScope</a></td><td class="text-right">1190</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LinkStatus.php.html#23"><abbr title="App\Enums\LinkStatus::values">values</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#31"><abbr title="App\Enums\LinkStatus::isBroken">isBroken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#39"><abbr title="App\Enums\LinkStatus::isTemporary">isTemporary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#50"><abbr title="App\Enums\LinkStatus::isSecurityIssue">isSecurityIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#61"><abbr title="App\Enums\LinkStatus::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#80"><abbr title="App\Enums\LinkStatus::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#94"><abbr title="App\Enums\LinkStatus::getRecommendedAction">getRecommendedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#113"><abbr title="App\Enums\LinkStatus::getConsoleColor">getConsoleColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#127"><abbr title="App\Enums\LinkStatus::getIcon">getIcon</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#146"><abbr title="App\Enums\LinkStatus::getGroup">getGroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#161"><abbr title="App\Enums\LinkStatus::getFormattedDisplay">getFormattedDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#173"><abbr title="App\Enums\LinkStatus::shouldRetry">shouldRetry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkStatus.php.html#184"><abbr title="App\Enums\LinkStatus::getHttpStatusCode">getHttpStatusCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#19"><abbr title="App\Enums\OutputFormat::values">values</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#27"><abbr title="App\Enums\OutputFormat::getSelectOptions">getSelectOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#41"><abbr title="App\Enums\OutputFormat::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#49"><abbr title="App\Enums\OutputFormat::getExtension">getExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#64"><abbr title="App\Enums\OutputFormat::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#79"><abbr title="App\Enums\OutputFormat::isStructured">isStructured</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#90"><abbr title="App\Enums\OutputFormat::supportsFormatting">supportsFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#101"><abbr title="App\Enums\OutputFormat::getFormatterClass">getFormatterClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#116"><abbr title="App\Enums\OutputFormat::getDefaultFilename">getDefaultFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#131"><abbr title="App\Enums\OutputFormat::isCiCdFriendly">isCiCdFriendly</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#142"><abbr title="App\Enums\OutputFormat::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#157"><abbr title="App\Enums\OutputFormat::getHelpText">getHelpText</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutputFormat.php.html#172"><abbr title="App\Enums\OutputFormat::getUseCases">getUseCases</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#20"><abbr title="App\Enums\ValidationScope::values">values</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#28"><abbr title="App\Enums\ValidationScope::names">names</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#36"><abbr title="App\Enums\ValidationScope::getSelectOptions">getSelectOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#50"><abbr title="App\Enums\ValidationScope::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#58"><abbr title="App\Enums\ValidationScope::fromString">fromString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#66"><abbr title="App\Enums\ValidationScope::includesExternal">includesExternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#74"><abbr title="App\Enums\ValidationScope::includesInternal">includesInternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#82"><abbr title="App\Enums\ValidationScope::includesAnchor">includesAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#90"><abbr title="App\Enums\ValidationScope::includesImage">includesImage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#98"><abbr title="App\Enums\ValidationScope::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#112"><abbr title="App\Enums\ValidationScope::getHelpText">getHelpText</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#126"><abbr title="App\Enums\ValidationScope::getIncludedScopes">getIncludedScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationScope.php.html#137"><abbr title="App\Enums\ValidationScope::getPriority">getPriority</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LinkStatus.php.html#61"><abbr title="App\Enums\LinkStatus::getDescription">getDescription</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="LinkStatus.php.html#94"><abbr title="App\Enums\LinkStatus::getRecommendedAction">getRecommendedAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="LinkStatus.php.html#127"><abbr title="App\Enums\LinkStatus::getIcon">getIcon</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="LinkStatus.php.html#184"><abbr title="App\Enums\LinkStatus::getHttpStatusCode">getHttpStatusCode</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="LinkStatus.php.html#146"><abbr title="App\Enums\LinkStatus::getGroup">getGroup</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutputFormat.php.html#49"><abbr title="App\Enums\OutputFormat::getExtension">getExtension</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutputFormat.php.html#64"><abbr title="App\Enums\OutputFormat::getMimeType">getMimeType</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutputFormat.php.html#101"><abbr title="App\Enums\OutputFormat::getFormatterClass">getFormatterClass</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutputFormat.php.html#116"><abbr title="App\Enums\OutputFormat::getDefaultFilename">getDefaultFilename</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutputFormat.php.html#142"><abbr title="App\Enums\OutputFormat::getDescription">getDescription</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutputFormat.php.html#157"><abbr title="App\Enums\OutputFormat::getHelpText">getHelpText</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutputFormat.php.html#172"><abbr title="App\Enums\OutputFormat::getUseCases">getUseCases</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="LinkStatus.php.html#80"><abbr title="App\Enums\LinkStatus::getSeverity">getSeverity</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="LinkStatus.php.html#113"><abbr title="App\Enums\LinkStatus::getConsoleColor">getConsoleColor</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ValidationScope.php.html#98"><abbr title="App\Enums\ValidationScope::getDescription">getDescription</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ValidationScope.php.html#112"><abbr title="App\Enums\ValidationScope::getHelpText">getHelpText</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ValidationScope.php.html#137"><abbr title="App\Enums\ValidationScope::getPriority">getPriority</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="LinkStatus.php.html#39"><abbr title="App\Enums\LinkStatus::isTemporary">isTemporary</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LinkStatus.php.html#50"><abbr title="App\Enums\LinkStatus::isSecurityIssue">isSecurityIssue</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LinkStatus.php.html#173"><abbr title="App\Enums\LinkStatus::shouldRetry">shouldRetry</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OutputFormat.php.html#79"><abbr title="App\Enums\OutputFormat::isStructured">isStructured</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OutputFormat.php.html#90"><abbr title="App\Enums\OutputFormat::supportsFormatting">supportsFormatting</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OutputFormat.php.html#131"><abbr title="App\Enums\OutputFormat::isCiCdFriendly">isCiCdFriendly</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ValidationScope.php.html#126"><abbr title="App\Enums\ValidationScope::getIncludedScopes">getIncludedScopes</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ValidationScope.php.html#66"><abbr title="App\Enums\ValidationScope::includesExternal">includesExternal</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ValidationScope.php.html#74"><abbr title="App\Enums\ValidationScope::includesInternal">includesInternal</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ValidationScope.php.html#82"><abbr title="App\Enums\ValidationScope::includesAnchor">includesAnchor</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ValidationScope.php.html#90"><abbr title="App\Enums\ValidationScope::includesImage">includesImage</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.10</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.10</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Fri Jul 25 13:37:46 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([39,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,72,"<a href=\"LinkStatus.php.html#7\">App\\Enums\\LinkStatus<\/a>"],[0,61,"<a href=\"OutputFormat.php.html#7\">App\\Enums\\OutputFormat<\/a>"],[0,34,"<a href=\"ValidationScope.php.html#9\">App\\Enums\\ValidationScope<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"LinkStatus.php.html#23\">App\\Enums\\LinkStatus::values<\/a>"],[0,1,"<a href=\"LinkStatus.php.html#31\">App\\Enums\\LinkStatus::isBroken<\/a>"],[0,3,"<a href=\"LinkStatus.php.html#39\">App\\Enums\\LinkStatus::isTemporary<\/a>"],[0,3,"<a href=\"LinkStatus.php.html#50\">App\\Enums\\LinkStatus::isSecurityIssue<\/a>"],[0,11,"<a href=\"LinkStatus.php.html#61\">App\\Enums\\LinkStatus::getDescription<\/a>"],[0,6,"<a href=\"LinkStatus.php.html#80\">App\\Enums\\LinkStatus::getSeverity<\/a>"],[0,11,"<a href=\"LinkStatus.php.html#94\">App\\Enums\\LinkStatus::getRecommendedAction<\/a>"],[0,6,"<a href=\"LinkStatus.php.html#113\">App\\Enums\\LinkStatus::getConsoleColor<\/a>"],[0,11,"<a href=\"LinkStatus.php.html#127\">App\\Enums\\LinkStatus::getIcon<\/a>"],[0,7,"<a href=\"LinkStatus.php.html#146\">App\\Enums\\LinkStatus::getGroup<\/a>"],[0,1,"<a href=\"LinkStatus.php.html#161\">App\\Enums\\LinkStatus::getFormattedDisplay<\/a>"],[0,3,"<a href=\"LinkStatus.php.html#173\">App\\Enums\\LinkStatus::shouldRetry<\/a>"],[0,8,"<a href=\"LinkStatus.php.html#184\">App\\Enums\\LinkStatus::getHttpStatusCode<\/a>"],[0,1,"<a href=\"OutputFormat.php.html#19\">App\\Enums\\OutputFormat::values<\/a>"],[0,1,"<a href=\"OutputFormat.php.html#27\">App\\Enums\\OutputFormat::getSelectOptions<\/a>"],[0,1,"<a href=\"OutputFormat.php.html#41\">App\\Enums\\OutputFormat::isValid<\/a>"],[0,7,"<a href=\"OutputFormat.php.html#49\">App\\Enums\\OutputFormat::getExtension<\/a>"],[0,7,"<a href=\"OutputFormat.php.html#64\">App\\Enums\\OutputFormat::getMimeType<\/a>"],[0,3,"<a href=\"OutputFormat.php.html#79\">App\\Enums\\OutputFormat::isStructured<\/a>"],[0,3,"<a href=\"OutputFormat.php.html#90\">App\\Enums\\OutputFormat::supportsFormatting<\/a>"],[0,7,"<a href=\"OutputFormat.php.html#101\">App\\Enums\\OutputFormat::getFormatterClass<\/a>"],[0,7,"<a href=\"OutputFormat.php.html#116\">App\\Enums\\OutputFormat::getDefaultFilename<\/a>"],[0,3,"<a href=\"OutputFormat.php.html#131\">App\\Enums\\OutputFormat::isCiCdFriendly<\/a>"],[0,7,"<a href=\"OutputFormat.php.html#142\">App\\Enums\\OutputFormat::getDescription<\/a>"],[0,7,"<a href=\"OutputFormat.php.html#157\">App\\Enums\\OutputFormat::getHelpText<\/a>"],[0,7,"<a href=\"OutputFormat.php.html#172\">App\\Enums\\OutputFormat::getUseCases<\/a>"],[0,1,"<a href=\"ValidationScope.php.html#20\">App\\Enums\\ValidationScope::values<\/a>"],[0,1,"<a href=\"ValidationScope.php.html#28\">App\\Enums\\ValidationScope::names<\/a>"],[0,1,"<a href=\"ValidationScope.php.html#36\">App\\Enums\\ValidationScope::getSelectOptions<\/a>"],[0,1,"<a href=\"ValidationScope.php.html#50\">App\\Enums\\ValidationScope::isValid<\/a>"],[0,1,"<a href=\"ValidationScope.php.html#58\">App\\Enums\\ValidationScope::fromString<\/a>"],[0,2,"<a href=\"ValidationScope.php.html#66\">App\\Enums\\ValidationScope::includesExternal<\/a>"],[0,2,"<a href=\"ValidationScope.php.html#74\">App\\Enums\\ValidationScope::includesInternal<\/a>"],[0,2,"<a href=\"ValidationScope.php.html#82\">App\\Enums\\ValidationScope::includesAnchor<\/a>"],[0,2,"<a href=\"ValidationScope.php.html#90\">App\\Enums\\ValidationScope::includesImage<\/a>"],[0,6,"<a href=\"ValidationScope.php.html#98\">App\\Enums\\ValidationScope::getDescription<\/a>"],[0,6,"<a href=\"ValidationScope.php.html#112\">App\\Enums\\ValidationScope::getHelpText<\/a>"],[0,3,"<a href=\"ValidationScope.php.html#126\">App\\Enums\\ValidationScope::getIncludedScopes<\/a>"],[0,6,"<a href=\"ValidationScope.php.html#137\">App\\Enums\\ValidationScope::getPriority<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
