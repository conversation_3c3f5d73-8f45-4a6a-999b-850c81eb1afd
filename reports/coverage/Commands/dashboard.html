<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Herd/validate-links/app/Commands</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/Users/<USER>/Herd/validate-links/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Commands</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseValidationCommand.php.html#15">App\Commands\BaseValidationCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigCommand.php.html#13">App\Commands\ConfigCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixCommand.php.html#13">App\Commands\FixCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InspireCommand.php.html#12">App\Commands\InspireCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportCommand.php.html#11">App\Commands\ReportCommand</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#21">App\Commands\ValidateCommand</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ValidateCommand.php.html#21">App\Commands\ValidateCommand</a></td><td class="text-right">4692</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#15">App\Commands\BaseValidationCommand</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="ConfigCommand.php.html#13">App\Commands\ConfigCommand</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="FixCommand.php.html#13">App\Commands\FixCommand</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ReportCommand.php.html#11">App\Commands\ReportCommand</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseValidationCommand.php.html#21"><abbr title="App\Commands\BaseValidationCommand::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#34"><abbr title="App\Commands\BaseValidationCommand::createConfigFromOptions">createConfigFromOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#53"><abbr title="App\Commands\BaseValidationCommand::parseScopes">parseScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#70"><abbr title="App\Commands\BaseValidationCommand::getOutputFormat">getOutputFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#78"><abbr title="App\Commands\BaseValidationCommand::validateCommandOptions">validateCommandOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#101"><abbr title="App\Commands\BaseValidationCommand::processValidationResults">processValidationResults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#127"><abbr title="App\Commands\BaseValidationCommand::displayResults">displayResults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#156"><abbr title="App\Commands\BaseValidationCommand::handleValidationError">handleValidationError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#170"><abbr title="App\Commands\BaseValidationCommand::isValidScopeList">isValidScopeList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#186"><abbr title="App\Commands\BaseValidationCommand::groupLinksByStatus">groupLinksByStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#201"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroups">displayStatusGroups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#212"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroup">displayStatusGroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#234"><abbr title="App\Commands\BaseValidationCommand::displayValidationSummary">displayValidationSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigCommand.php.html#21"><abbr title="App\Commands\ConfigCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigCommand.php.html#36"><abbr title="App\Commands\ConfigCommand::initializeConfig">initializeConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigCommand.php.html#63"><abbr title="App\Commands\ConfigCommand::showConfig">showConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigCommand.php.html#74"><abbr title="App\Commands\ConfigCommand::gatherConfigurationSettings">gatherConfigurationSettings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixCommand.php.html#23"><abbr title="App\Commands\FixCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixCommand.php.html#46"><abbr title="App\Commands\FixCommand::handleInteractiveFix">handleInteractiveFix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixCommand.php.html#64"><abbr title="App\Commands\FixCommand::handleAutomaticFix">handleAutomaticFix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InspireCommand.php.html#31"><abbr title="App\Commands\InspireCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InspireCommand.php.html#46"><abbr title="App\Commands\InspireCommand::schedule">schedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportCommand.php.html#21"><abbr title="App\Commands\ReportCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#49"><abbr title="App\Commands\ValidateCommand::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#61"><abbr title="App\Commands\ValidateCommand::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#80"><abbr title="App\Commands\ValidateCommand::handleInteractive">handleInteractive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#111"><abbr title="App\Commands\ValidateCommand::gatherPaths">gatherPaths</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#138"><abbr title="App\Commands\ValidateCommand::gatherValidationScope">gatherValidationScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#170"><abbr title="App\Commands\ValidateCommand::getScopeOptions">getScopeOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#186"><abbr title="App\Commands\ValidateCommand::gatherOutputConfiguration">gatherOutputConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#239"><abbr title="App\Commands\ValidateCommand::validateOutputPath">validateOutputPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#259"><abbr title="App\Commands\ValidateCommand::gatherAdvancedOptions">gatherAdvancedOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#306"><abbr title="App\Commands\ValidateCommand::confirmConfiguration">confirmConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#326"><abbr title="App\Commands\ValidateCommand::executeInteractiveValidation">executeInteractiveValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#379"><abbr title="App\Commands\ValidateCommand::countFiles">countFiles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#396"><abbr title="App\Commands\ValidateCommand::getFilesFromPath">getFilesFromPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#419"><abbr title="App\Commands\ValidateCommand::hasBrokenLinks">hasBrokenLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#433"><abbr title="App\Commands\ValidateCommand::displaySummary">displaySummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#453"><abbr title="App\Commands\ValidateCommand::handleNonInteractive">handleNonInteractive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCommand.php.html#468"><abbr title="App\Commands\ValidateCommand::performValidation">performValidation</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ValidateCommand.php.html#259"><abbr title="App\Commands\ValidateCommand::gatherAdvancedOptions">gatherAdvancedOptions</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ValidateCommand.php.html#468"><abbr title="App\Commands\ValidateCommand::performValidation">performValidation</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ValidateCommand.php.html#326"><abbr title="App\Commands\ValidateCommand::executeInteractiveValidation">executeInteractiveValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#127"><abbr title="App\Commands\BaseValidationCommand::displayResults">displayResults</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#212"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroup">displayStatusGroup</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ValidateCommand.php.html#186"><abbr title="App\Commands\ValidateCommand::gatherOutputConfiguration">gatherOutputConfiguration</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ValidateCommand.php.html#396"><abbr title="App\Commands\ValidateCommand::getFilesFromPath">getFilesFromPath</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#78"><abbr title="App\Commands\BaseValidationCommand::validateCommandOptions">validateCommandOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ConfigCommand.php.html#36"><abbr title="App\Commands\ConfigCommand::initializeConfig">initializeConfig</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ValidateCommand.php.html#239"><abbr title="App\Commands\ValidateCommand::validateOutputPath">validateOutputPath</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ValidateCommand.php.html#379"><abbr title="App\Commands\ValidateCommand::countFiles">countFiles</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ValidateCommand.php.html#419"><abbr title="App\Commands\ValidateCommand::hasBrokenLinks">hasBrokenLinks</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#170"><abbr title="App\Commands\BaseValidationCommand::isValidScopeList">isValidScopeList</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ConfigCommand.php.html#21"><abbr title="App\Commands\ConfigCommand::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FixCommand.php.html#23"><abbr title="App\Commands\FixCommand::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ValidateCommand.php.html#61"><abbr title="App\Commands\ValidateCommand::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ValidateCommand.php.html#111"><abbr title="App\Commands\ValidateCommand::gatherPaths">gatherPaths</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ValidateCommand.php.html#138"><abbr title="App\Commands\ValidateCommand::gatherValidationScope">gatherValidationScope</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ValidateCommand.php.html#170"><abbr title="App\Commands\ValidateCommand::getScopeOptions">getScopeOptions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ValidateCommand.php.html#306"><abbr title="App\Commands\ValidateCommand::confirmConfiguration">confirmConfiguration</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#53"><abbr title="App\Commands\BaseValidationCommand::parseScopes">parseScopes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#101"><abbr title="App\Commands\BaseValidationCommand::processValidationResults">processValidationResults</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#156"><abbr title="App\Commands\BaseValidationCommand::handleValidationError">handleValidationError</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#186"><abbr title="App\Commands\BaseValidationCommand::groupLinksByStatus">groupLinksByStatus</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BaseValidationCommand.php.html#201"><abbr title="App\Commands\BaseValidationCommand::displayStatusGroups">displayStatusGroups</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FixCommand.php.html#46"><abbr title="App\Commands\FixCommand::handleInteractiveFix">handleInteractiveFix</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FixCommand.php.html#64"><abbr title="App\Commands\FixCommand::handleAutomaticFix">handleAutomaticFix</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReportCommand.php.html#21"><abbr title="App\Commands\ReportCommand::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ValidateCommand.php.html#80"><abbr title="App\Commands\ValidateCommand::handleInteractive">handleInteractive</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ValidateCommand.php.html#433"><abbr title="App\Commands\ValidateCommand::displaySummary">displaySummary</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.10</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.10</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Fri Jul 25 13:37:46 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([40,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,31,"<a href=\"BaseValidationCommand.php.html#15\">App\\Commands\\BaseValidationCommand<\/a>"],[0,9,"<a href=\"ConfigCommand.php.html#13\">App\\Commands\\ConfigCommand<\/a>"],[0,7,"<a href=\"FixCommand.php.html#13\">App\\Commands\\FixCommand<\/a>"],[0,2,"<a href=\"InspireCommand.php.html#12\">App\\Commands\\InspireCommand<\/a>"],[0,2,"<a href=\"ReportCommand.php.html#11\">App\\Commands\\ReportCommand<\/a>"],[0,68,"<a href=\"ValidateCommand.php.html#21\">App\\Commands\\ValidateCommand<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseValidationCommand.php.html#21\">App\\Commands\\BaseValidationCommand::__construct<\/a>"],[0,1,"<a href=\"BaseValidationCommand.php.html#34\">App\\Commands\\BaseValidationCommand::createConfigFromOptions<\/a>"],[0,2,"<a href=\"BaseValidationCommand.php.html#53\">App\\Commands\\BaseValidationCommand::parseScopes<\/a>"],[0,1,"<a href=\"BaseValidationCommand.php.html#70\">App\\Commands\\BaseValidationCommand::getOutputFormat<\/a>"],[0,4,"<a href=\"BaseValidationCommand.php.html#78\">App\\Commands\\BaseValidationCommand::validateCommandOptions<\/a>"],[0,2,"<a href=\"BaseValidationCommand.php.html#101\">App\\Commands\\BaseValidationCommand::processValidationResults<\/a>"],[0,5,"<a href=\"BaseValidationCommand.php.html#127\">App\\Commands\\BaseValidationCommand::displayResults<\/a>"],[0,2,"<a href=\"BaseValidationCommand.php.html#156\">App\\Commands\\BaseValidationCommand::handleValidationError<\/a>"],[0,3,"<a href=\"BaseValidationCommand.php.html#170\">App\\Commands\\BaseValidationCommand::isValidScopeList<\/a>"],[0,2,"<a href=\"BaseValidationCommand.php.html#186\">App\\Commands\\BaseValidationCommand::groupLinksByStatus<\/a>"],[0,2,"<a href=\"BaseValidationCommand.php.html#201\">App\\Commands\\BaseValidationCommand::displayStatusGroups<\/a>"],[0,5,"<a href=\"BaseValidationCommand.php.html#212\">App\\Commands\\BaseValidationCommand::displayStatusGroup<\/a>"],[0,1,"<a href=\"BaseValidationCommand.php.html#234\">App\\Commands\\BaseValidationCommand::displayValidationSummary<\/a>"],[0,3,"<a href=\"ConfigCommand.php.html#21\">App\\Commands\\ConfigCommand::handle<\/a>"],[0,4,"<a href=\"ConfigCommand.php.html#36\">App\\Commands\\ConfigCommand::initializeConfig<\/a>"],[0,1,"<a href=\"ConfigCommand.php.html#63\">App\\Commands\\ConfigCommand::showConfig<\/a>"],[0,1,"<a href=\"ConfigCommand.php.html#74\">App\\Commands\\ConfigCommand::gatherConfigurationSettings<\/a>"],[0,3,"<a href=\"FixCommand.php.html#23\">App\\Commands\\FixCommand::handle<\/a>"],[0,2,"<a href=\"FixCommand.php.html#46\">App\\Commands\\FixCommand::handleInteractiveFix<\/a>"],[0,2,"<a href=\"FixCommand.php.html#64\">App\\Commands\\FixCommand::handleAutomaticFix<\/a>"],[0,1,"<a href=\"InspireCommand.php.html#31\">App\\Commands\\InspireCommand::handle<\/a>"],[0,1,"<a href=\"InspireCommand.php.html#46\">App\\Commands\\InspireCommand::schedule<\/a>"],[0,2,"<a href=\"ReportCommand.php.html#21\">App\\Commands\\ReportCommand::handle<\/a>"],[0,1,"<a href=\"ValidateCommand.php.html#49\">App\\Commands\\ValidateCommand::__construct<\/a>"],[0,3,"<a href=\"ValidateCommand.php.html#61\">App\\Commands\\ValidateCommand::handle<\/a>"],[0,2,"<a href=\"ValidateCommand.php.html#80\">App\\Commands\\ValidateCommand::handleInteractive<\/a>"],[0,3,"<a href=\"ValidateCommand.php.html#111\">App\\Commands\\ValidateCommand::gatherPaths<\/a>"],[0,3,"<a href=\"ValidateCommand.php.html#138\">App\\Commands\\ValidateCommand::gatherValidationScope<\/a>"],[0,3,"<a href=\"ValidateCommand.php.html#170\">App\\Commands\\ValidateCommand::getScopeOptions<\/a>"],[0,5,"<a href=\"ValidateCommand.php.html#186\">App\\Commands\\ValidateCommand::gatherOutputConfiguration<\/a>"],[0,4,"<a href=\"ValidateCommand.php.html#239\">App\\Commands\\ValidateCommand::validateOutputPath<\/a>"],[0,12,"<a href=\"ValidateCommand.php.html#259\">App\\Commands\\ValidateCommand::gatherAdvancedOptions<\/a>"],[0,3,"<a href=\"ValidateCommand.php.html#306\">App\\Commands\\ValidateCommand::confirmConfiguration<\/a>"],[0,6,"<a href=\"ValidateCommand.php.html#326\">App\\Commands\\ValidateCommand::executeInteractiveValidation<\/a>"],[0,4,"<a href=\"ValidateCommand.php.html#379\">App\\Commands\\ValidateCommand::countFiles<\/a>"],[0,5,"<a href=\"ValidateCommand.php.html#396\">App\\Commands\\ValidateCommand::getFilesFromPath<\/a>"],[0,4,"<a href=\"ValidateCommand.php.html#419\">App\\Commands\\ValidateCommand::hasBrokenLinks<\/a>"],[0,2,"<a href=\"ValidateCommand.php.html#433\">App\\Commands\\ValidateCommand::displaySummary<\/a>"],[0,1,"<a href=\"ValidateCommand.php.html#453\">App\\Commands\\ValidateCommand::handleNonInteractive<\/a>"],[0,7,"<a href=\"ValidateCommand.php.html#468\">App\\Commands\\ValidateCommand::performValidation<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
