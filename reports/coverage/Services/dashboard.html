<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Herd/validate-links/app/Services</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/Users/<USER>/Herd/validate-links/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Services</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ConcurrentValidationService.php.html#14">App\Services\ConcurrentValidationService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#9">App\Services\Formatters\ConsoleFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/HtmlFormatter.php.html#9">App\Services\Formatters\HtmlFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/JsonFormatter.php.html#9">App\Services\Formatters\JsonFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/MarkdownFormatter.php.html#9">App\Services\Formatters\MarkdownFormatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GitHubAnchorService.php.html#9">App\Services\GitHubAnchorService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#12">App\Services\LinkValidationService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PluginManager.php.html#12">App\Services\PluginManager</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportingService.php.html#17">App\Services\ReportingService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#10">App\Services\SecurityValidationService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#9">App\Services\StatisticsService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#11">App\Services\ValueObjects\ValidationConfig</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#10">App\Services\ValueObjects\ValidationResult</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#11">App\Services\ValueObjects\ValidationConfig</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="LinkValidationService.php.html#12">App\Services\LinkValidationService</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#10">App\Services\SecurityValidationService</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="ReportingService.php.html#17">App\Services\ReportingService</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#9">App\Services\Formatters\ConsoleFormatter</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Formatters/HtmlFormatter.php.html#9">App\Services\Formatters\HtmlFormatter</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="GitHubAnchorService.php.html#9">App\Services\GitHubAnchorService</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="StatisticsService.php.html#9">App\Services\StatisticsService</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Formatters/MarkdownFormatter.php.html#9">App\Services\Formatters\MarkdownFormatter</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ConcurrentValidationService.php.html#14">App\Services\ConcurrentValidationService</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Formatters/JsonFormatter.php.html#9">App\Services\Formatters\JsonFormatter</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ConcurrentValidationService.php.html#20"><abbr title="App\Services\ConcurrentValidationService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConcurrentValidationService.php.html#31"><abbr title="App\Services\ConcurrentValidationService::validateExternalLinksConcurrently">validateExternalLinksConcurrently</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConcurrentValidationService.php.html#52"><abbr title="App\Services\ConcurrentValidationService::createRequests">createRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#14"><abbr title="App\Services\Formatters\ConsoleFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#35"><abbr title="App\Services\Formatters\ConsoleFormatter::formatHeader">formatHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#43"><abbr title="App\Services\Formatters\ConsoleFormatter::formatSummary">formatSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#57"><abbr title="App\Services\Formatters\ConsoleFormatter::formatBrokenLinks">formatBrokenLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#71"><abbr title="App\Services\Formatters\ConsoleFormatter::formatStatistics">formatStatistics</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/HtmlFormatter.php.html#11"><abbr title="App\Services\Formatters\HtmlFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/HtmlFormatter.php.html#19"><abbr title="App\Services\Formatters\HtmlFormatter::generateContent">generateContent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/HtmlFormatter.php.html#99"><abbr title="App\Services\Formatters\HtmlFormatter::getHtmlTemplate">getHtmlTemplate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/JsonFormatter.php.html#14"><abbr title="App\Services\Formatters\JsonFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatters/MarkdownFormatter.php.html#11"><abbr title="App\Services\Formatters\MarkdownFormatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GitHubAnchorService.php.html#14"><abbr title="App\Services\GitHubAnchorService::generateAnchor">generateAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GitHubAnchorService.php.html#37"><abbr title="App\Services\GitHubAnchorService::validateAnchor">validateAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GitHubAnchorService.php.html#48"><abbr title="App\Services\GitHubAnchorService::extractAnchors">extractAnchors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GitHubAnchorService.php.html#79"><abbr title="App\Services\GitHubAnchorService::normalizeAnchor">normalizeAnchor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#14"><abbr title="App\Services\LinkValidationService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#20"><abbr title="App\Services\LinkValidationService::validateFile">validateFile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#51"><abbr title="App\Services\LinkValidationService::extractLinks">extractLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#78"><abbr title="App\Services\LinkValidationService::categorizeLinks">categorizeLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#102"><abbr title="App\Services\LinkValidationService::validateCrossReferences">validateCrossReferences</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#120"><abbr title="App\Services\LinkValidationService::extractAllAnchors">extractAllAnchors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LinkValidationService.php.html#133"><abbr title="App\Services\LinkValidationService::validateCrossReference">validateCrossReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PluginManager.php.html#16"><abbr title="App\Services\PluginManager::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PluginManager.php.html#21"><abbr title="App\Services\PluginManager::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PluginManager.php.html#27"><abbr title="App\Services\PluginManager::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PluginManager.php.html#34"><abbr title="App\Services\PluginManager::getPlugin">getPlugin</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportingService.php.html#23"><abbr title="App\Services\ReportingService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportingService.php.html#37"><abbr title="App\Services\ReportingService::generateReport">generateReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportingService.php.html#52"><abbr title="App\Services\ReportingService::addFormatter">addFormatter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportingService.php.html#60"><abbr title="App\Services\ReportingService::exportReport">exportReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportingService.php.html#79"><abbr title="App\Services\ReportingService::getAvailableFormats">getAvailableFormats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportingService.php.html#87"><abbr title="App\Services\ReportingService::generateSummary">generateSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#20"><abbr title="App\Services\SecurityValidationService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#39"><abbr title="App\Services\SecurityValidationService::validateUrl">validateUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#74"><abbr title="App\Services\SecurityValidationService::validatePath">validatePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#105"><abbr title="App\Services\SecurityValidationService::isDomainBlocked">isDomainBlocked</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#113"><abbr title="App\Services\SecurityValidationService::isProtocolAllowed">isProtocolAllowed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#121"><abbr title="App\Services\SecurityValidationService::validateFileSize">validateFileSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#139"><abbr title="App\Services\SecurityValidationService::getSecurityConfig">getSecurityConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#152"><abbr title="App\Services\SecurityValidationService::isPrivateIp">isPrivateIp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#17"><abbr title="App\Services\StatisticsService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#25"><abbr title="App\Services\StatisticsService::recordValidation">recordValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#44"><abbr title="App\Services\StatisticsService::recordFileProcessed">recordFileProcessed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#59"><abbr title="App\Services\StatisticsService::getStatistics">getStatistics</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#71"><abbr title="App\Services\StatisticsService::getBrokenLinks">getBrokenLinks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#79"><abbr title="App\Services\StatisticsService::getProcessedFiles">getProcessedFiles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#87"><abbr title="App\Services\StatisticsService::reset">reset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatisticsService.php.html#105"><abbr title="App\Services\StatisticsService::getSuccessRate">getSuccessRate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#16"><abbr title="App\Services\ValueObjects\ValidationConfig::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#33"><abbr title="App\Services\ValueObjects\ValidationConfig::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#51"><abbr title="App\Services\ValueObjects\ValidationConfig::withDefaults">withDefaults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#61"><abbr title="App\Services\ValueObjects\ValidationConfig::getScopes">getScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#69"><abbr title="App\Services\ValueObjects\ValidationConfig::hasScope">hasScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#80"><abbr title="App\Services\ValueObjects\ValidationConfig::getEffectiveScopes">getEffectiveScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#89"><abbr title="App\Services\ValueObjects\ValidationConfig::getTimeout">getTimeout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#94"><abbr title="App\Services\ValueObjects\ValidationConfig::getMaxRedirects">getMaxRedirects</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#99"><abbr title="App\Services\ValueObjects\ValidationConfig::getConcurrentRequests">getConcurrentRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#104"><abbr title="App\Services\ValueObjects\ValidationConfig::isCacheEnabled">isCacheEnabled</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#109"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldFollowRedirects">shouldFollowRedirects</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#114"><abbr title="App\Services\ValueObjects\ValidationConfig::getUserAgent">getUserAgent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#119"><abbr title="App\Services\ValueObjects\ValidationConfig::getOutputFormat">getOutputFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#124"><abbr title="App\Services\ValueObjects\ValidationConfig::getAdditionalOptions">getAdditionalOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#129"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateInternal">shouldValidateInternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#134"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateExternal">shouldValidateExternal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#139"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateAnchors">shouldValidateAnchors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#144"><abbr title="App\Services\ValueObjects\ValidationConfig::shouldValidateCrossReferences">shouldValidateCrossReferences</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#154"><abbr title="App\Services\ValueObjects\ValidationConfig::withScopes">withScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#172"><abbr title="App\Services\ValueObjects\ValidationConfig::withOutputFormat">withOutputFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#187"><abbr title="App\Services\ValueObjects\ValidationConfig::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#202"><abbr title="App\Services\ValueObjects\ValidationConfig::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#12"><abbr title="App\Services\ValueObjects\ValidationResult::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#26"><abbr title="App\Services\ValueObjects\ValidationResult::success">success</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#46"><abbr title="App\Services\ValueObjects\ValidationResult::failure">failure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#68"><abbr title="App\Services\ValueObjects\ValidationResult::getUrl">getUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#73"><abbr title="App\Services\ValueObjects\ValidationResult::getStatus">getStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#78"><abbr title="App\Services\ValueObjects\ValidationResult::getScope">getScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#83"><abbr title="App\Services\ValueObjects\ValidationResult::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#88"><abbr title="App\Services\ValueObjects\ValidationResult::isBroken">isBroken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#93"><abbr title="App\Services\ValueObjects\ValidationResult::isTemporary">isTemporary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#98"><abbr title="App\Services\ValueObjects\ValidationResult::isSecurityIssue">isSecurityIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#103"><abbr title="App\Services\ValueObjects\ValidationResult::getError">getError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#108"><abbr title="App\Services\ValueObjects\ValidationResult::getHttpStatusCode">getHttpStatusCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#113"><abbr title="App\Services\ValueObjects\ValidationResult::getRedirectUrl">getRedirectUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#118"><abbr title="App\Services\ValueObjects\ValidationResult::getResponseTime">getResponseTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#123"><abbr title="App\Services\ValueObjects\ValidationResult::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#131"><abbr title="App\Services\ValueObjects\ValidationResult::getSeverity">getSeverity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#139"><abbr title="App\Services\ValueObjects\ValidationResult::getRecommendedAction">getRecommendedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#147"><abbr title="App\Services\ValueObjects\ValidationResult::getConsoleColor">getConsoleColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#155"><abbr title="App\Services\ValueObjects\ValidationResult::getFormattedDisplay">getFormattedDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#163"><abbr title="App\Services\ValueObjects\ValidationResult::shouldRetry">shouldRetry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#168"><abbr title="App\Services\ValueObjects\ValidationResult::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValueObjects/ValidationResult.php.html#189"><abbr title="App\Services\ValueObjects\ValidationResult::toJson">toJson</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#202"><abbr title="App\Services\ValueObjects\ValidationConfig::validate">validate</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Formatters/HtmlFormatter.php.html#19"><abbr title="App\Services\Formatters\HtmlFormatter::generateContent">generateContent</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#39"><abbr title="App\Services\SecurityValidationService::validateUrl">validateUrl</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="LinkValidationService.php.html#20"><abbr title="App\Services\LinkValidationService::validateFile">validateFile</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="GitHubAnchorService.php.html#48"><abbr title="App\Services\GitHubAnchorService::extractAnchors">extractAnchors</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Formatters/MarkdownFormatter.php.html#11"><abbr title="App\Services\Formatters\MarkdownFormatter::format">format</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ReportingService.php.html#87"><abbr title="App\Services\ReportingService::generateSummary">generateSummary</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#74"><abbr title="App\Services\SecurityValidationService::validatePath">validatePath</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="LinkValidationService.php.html#78"><abbr title="App\Services\LinkValidationService::categorizeLinks">categorizeLinks</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#71"><abbr title="App\Services\Formatters\ConsoleFormatter::formatStatistics">formatStatistics</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="LinkValidationService.php.html#102"><abbr title="App\Services\LinkValidationService::validateCrossReferences">validateCrossReferences</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="LinkValidationService.php.html#133"><abbr title="App\Services\LinkValidationService::validateCrossReference">validateCrossReference</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#35"><abbr title="App\Services\Formatters\ConsoleFormatter::formatHeader">formatHeader</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LinkValidationService.php.html#51"><abbr title="App\Services\LinkValidationService::extractLinks">extractLinks</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ReportingService.php.html#60"><abbr title="App\Services\ReportingService::exportReport">exportReport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#121"><abbr title="App\Services\SecurityValidationService::validateFileSize">validateFileSize</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SecurityValidationService.php.html#152"><abbr title="App\Services\SecurityValidationService::isPrivateIp">isPrivateIp</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ConcurrentValidationService.php.html#52"><abbr title="App\Services\ConcurrentValidationService::createRequests">createRequests</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#14"><abbr title="App\Services\Formatters\ConsoleFormatter::format">format</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Formatters/ConsoleFormatter.php.html#57"><abbr title="App\Services\Formatters\ConsoleFormatter::formatBrokenLinks">formatBrokenLinks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Formatters/JsonFormatter.php.html#14"><abbr title="App\Services\Formatters\JsonFormatter::format">format</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LinkValidationService.php.html#120"><abbr title="App\Services\LinkValidationService::extractAllAnchors">extractAllAnchors</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReportingService.php.html#37"><abbr title="App\Services\ReportingService::generateReport">generateReport</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StatisticsService.php.html#25"><abbr title="App\Services\StatisticsService::recordValidation">recordValidation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StatisticsService.php.html#105"><abbr title="App\Services\StatisticsService::getSuccessRate">getSuccessRate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#69"><abbr title="App\Services\ValueObjects\ValidationConfig::hasScope">hasScope</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ValueObjects/ValidationConfig.php.html#80"><abbr title="App\Services\ValueObjects\ValidationConfig::getEffectiveScopes">getEffectiveScopes</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.10</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.10</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Fri Jul 25 13:37:46 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([13,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([94,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"ConcurrentValidationService.php.html#14\">App\\Services\\ConcurrentValidationService<\/a>"],[0,12,"<a href=\"Formatters\/ConsoleFormatter.php.html#9\">App\\Services\\Formatters\\ConsoleFormatter<\/a>"],[0,11,"<a href=\"Formatters\/HtmlFormatter.php.html#9\">App\\Services\\Formatters\\HtmlFormatter<\/a>"],[0,2,"<a href=\"Formatters\/JsonFormatter.php.html#9\">App\\Services\\Formatters\\JsonFormatter<\/a>"],[0,6,"<a href=\"Formatters\/MarkdownFormatter.php.html#9\">App\\Services\\Formatters\\MarkdownFormatter<\/a>"],[0,10,"<a href=\"GitHubAnchorService.php.html#9\">App\\Services\\GitHubAnchorService<\/a>"],[0,27,"<a href=\"LinkValidationService.php.html#12\">App\\Services\\LinkValidationService<\/a>"],[0,4,"<a href=\"PluginManager.php.html#12\">App\\Services\\PluginManager<\/a>"],[0,14,"<a href=\"ReportingService.php.html#17\">App\\Services\\ReportingService<\/a>"],[0,25,"<a href=\"SecurityValidationService.php.html#10\">App\\Services\\SecurityValidationService<\/a>"],[0,10,"<a href=\"StatisticsService.php.html#9\">App\\Services\\StatisticsService<\/a>"],[0,33,"<a href=\"ValueObjects\/ValidationConfig.php.html#11\">App\\Services\\ValueObjects\\ValidationConfig<\/a>"],[0,22,"<a href=\"ValueObjects\/ValidationResult.php.html#10\">App\\Services\\ValueObjects\\ValidationResult<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ConcurrentValidationService.php.html#20\">App\\Services\\ConcurrentValidationService::__construct<\/a>"],[0,1,"<a href=\"ConcurrentValidationService.php.html#31\">App\\Services\\ConcurrentValidationService::validateExternalLinksConcurrently<\/a>"],[0,2,"<a href=\"ConcurrentValidationService.php.html#52\">App\\Services\\ConcurrentValidationService::createRequests<\/a>"],[0,2,"<a href=\"Formatters\/ConsoleFormatter.php.html#14\">App\\Services\\Formatters\\ConsoleFormatter::format<\/a>"],[0,3,"<a href=\"Formatters\/ConsoleFormatter.php.html#35\">App\\Services\\Formatters\\ConsoleFormatter::formatHeader<\/a>"],[0,1,"<a href=\"Formatters\/ConsoleFormatter.php.html#43\">App\\Services\\Formatters\\ConsoleFormatter::formatSummary<\/a>"],[0,2,"<a href=\"Formatters\/ConsoleFormatter.php.html#57\">App\\Services\\Formatters\\ConsoleFormatter::formatBrokenLinks<\/a>"],[0,4,"<a href=\"Formatters\/ConsoleFormatter.php.html#71\">App\\Services\\Formatters\\ConsoleFormatter::formatStatistics<\/a>"],[0,1,"<a href=\"Formatters\/HtmlFormatter.php.html#11\">App\\Services\\Formatters\\HtmlFormatter::format<\/a>"],[0,9,"<a href=\"Formatters\/HtmlFormatter.php.html#19\">App\\Services\\Formatters\\HtmlFormatter::generateContent<\/a>"],[0,1,"<a href=\"Formatters\/HtmlFormatter.php.html#99\">App\\Services\\Formatters\\HtmlFormatter::getHtmlTemplate<\/a>"],[0,2,"<a href=\"Formatters\/JsonFormatter.php.html#14\">App\\Services\\Formatters\\JsonFormatter::format<\/a>"],[0,6,"<a href=\"Formatters\/MarkdownFormatter.php.html#11\">App\\Services\\Formatters\\MarkdownFormatter::format<\/a>"],[0,1,"<a href=\"GitHubAnchorService.php.html#14\">App\\Services\\GitHubAnchorService::generateAnchor<\/a>"],[0,1,"<a href=\"GitHubAnchorService.php.html#37\">App\\Services\\GitHubAnchorService::validateAnchor<\/a>"],[0,7,"<a href=\"GitHubAnchorService.php.html#48\">App\\Services\\GitHubAnchorService::extractAnchors<\/a>"],[0,1,"<a href=\"GitHubAnchorService.php.html#79\">App\\Services\\GitHubAnchorService::normalizeAnchor<\/a>"],[0,1,"<a href=\"LinkValidationService.php.html#14\">App\\Services\\LinkValidationService::__construct<\/a>"],[0,8,"<a href=\"LinkValidationService.php.html#20\">App\\Services\\LinkValidationService::validateFile<\/a>"],[0,3,"<a href=\"LinkValidationService.php.html#51\">App\\Services\\LinkValidationService::extractLinks<\/a>"],[0,5,"<a href=\"LinkValidationService.php.html#78\">App\\Services\\LinkValidationService::categorizeLinks<\/a>"],[0,4,"<a href=\"LinkValidationService.php.html#102\">App\\Services\\LinkValidationService::validateCrossReferences<\/a>"],[0,2,"<a href=\"LinkValidationService.php.html#120\">App\\Services\\LinkValidationService::extractAllAnchors<\/a>"],[0,4,"<a href=\"LinkValidationService.php.html#133\">App\\Services\\LinkValidationService::validateCrossReference<\/a>"],[0,1,"<a href=\"PluginManager.php.html#16\">App\\Services\\PluginManager::__construct<\/a>"],[0,1,"<a href=\"PluginManager.php.html#21\">App\\Services\\PluginManager::register<\/a>"],[0,1,"<a href=\"PluginManager.php.html#27\">App\\Services\\PluginManager::boot<\/a>"],[0,1,"<a href=\"PluginManager.php.html#34\">App\\Services\\PluginManager::getPlugin<\/a>"],[0,1,"<a href=\"ReportingService.php.html#23\">App\\Services\\ReportingService::__construct<\/a>"],[0,2,"<a href=\"ReportingService.php.html#37\">App\\Services\\ReportingService::generateReport<\/a>"],[0,1,"<a href=\"ReportingService.php.html#52\">App\\Services\\ReportingService::addFormatter<\/a>"],[0,3,"<a href=\"ReportingService.php.html#60\">App\\Services\\ReportingService::exportReport<\/a>"],[0,1,"<a href=\"ReportingService.php.html#79\">App\\Services\\ReportingService::getAvailableFormats<\/a>"],[0,6,"<a href=\"ReportingService.php.html#87\">App\\Services\\ReportingService::generateSummary<\/a>"],[0,1,"<a href=\"SecurityValidationService.php.html#20\">App\\Services\\SecurityValidationService::__construct<\/a>"],[0,9,"<a href=\"SecurityValidationService.php.html#39\">App\\Services\\SecurityValidationService::validateUrl<\/a>"],[0,6,"<a href=\"SecurityValidationService.php.html#74\">App\\Services\\SecurityValidationService::validatePath<\/a>"],[0,1,"<a href=\"SecurityValidationService.php.html#105\">App\\Services\\SecurityValidationService::isDomainBlocked<\/a>"],[0,1,"<a href=\"SecurityValidationService.php.html#113\">App\\Services\\SecurityValidationService::isProtocolAllowed<\/a>"],[0,3,"<a href=\"SecurityValidationService.php.html#121\">App\\Services\\SecurityValidationService::validateFileSize<\/a>"],[0,1,"<a href=\"SecurityValidationService.php.html#139\">App\\Services\\SecurityValidationService::getSecurityConfig<\/a>"],[0,3,"<a href=\"SecurityValidationService.php.html#152\">App\\Services\\SecurityValidationService::isPrivateIp<\/a>"],[0,1,"<a href=\"StatisticsService.php.html#17\">App\\Services\\StatisticsService::__construct<\/a>"],[0,2,"<a href=\"StatisticsService.php.html#25\">App\\Services\\StatisticsService::recordValidation<\/a>"],[0,1,"<a href=\"StatisticsService.php.html#44\">App\\Services\\StatisticsService::recordFileProcessed<\/a>"],[0,1,"<a href=\"StatisticsService.php.html#59\">App\\Services\\StatisticsService::getStatistics<\/a>"],[0,1,"<a href=\"StatisticsService.php.html#71\">App\\Services\\StatisticsService::getBrokenLinks<\/a>"],[0,1,"<a href=\"StatisticsService.php.html#79\">App\\Services\\StatisticsService::getProcessedFiles<\/a>"],[0,1,"<a href=\"StatisticsService.php.html#87\">App\\Services\\StatisticsService::reset<\/a>"],[0,2,"<a href=\"StatisticsService.php.html#105\">App\\Services\\StatisticsService::getSuccessRate<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#16\">App\\Services\\ValueObjects\\ValidationConfig::__construct<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#33\">App\\Services\\ValueObjects\\ValidationConfig::create<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#51\">App\\Services\\ValueObjects\\ValidationConfig::withDefaults<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#61\">App\\Services\\ValueObjects\\ValidationConfig::getScopes<\/a>"],[0,2,"<a href=\"ValueObjects\/ValidationConfig.php.html#69\">App\\Services\\ValueObjects\\ValidationConfig::hasScope<\/a>"],[0,2,"<a href=\"ValueObjects\/ValidationConfig.php.html#80\">App\\Services\\ValueObjects\\ValidationConfig::getEffectiveScopes<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#89\">App\\Services\\ValueObjects\\ValidationConfig::getTimeout<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#94\">App\\Services\\ValueObjects\\ValidationConfig::getMaxRedirects<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#99\">App\\Services\\ValueObjects\\ValidationConfig::getConcurrentRequests<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#104\">App\\Services\\ValueObjects\\ValidationConfig::isCacheEnabled<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#109\">App\\Services\\ValueObjects\\ValidationConfig::shouldFollowRedirects<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#114\">App\\Services\\ValueObjects\\ValidationConfig::getUserAgent<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#119\">App\\Services\\ValueObjects\\ValidationConfig::getOutputFormat<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#124\">App\\Services\\ValueObjects\\ValidationConfig::getAdditionalOptions<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#129\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateInternal<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#134\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateExternal<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#139\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateAnchors<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#144\">App\\Services\\ValueObjects\\ValidationConfig::shouldValidateCrossReferences<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#154\">App\\Services\\ValueObjects\\ValidationConfig::withScopes<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#172\">App\\Services\\ValueObjects\\ValidationConfig::withOutputFormat<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationConfig.php.html#187\">App\\Services\\ValueObjects\\ValidationConfig::toArray<\/a>"],[0,10,"<a href=\"ValueObjects\/ValidationConfig.php.html#202\">App\\Services\\ValueObjects\\ValidationConfig::validate<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#12\">App\\Services\\ValueObjects\\ValidationResult::__construct<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#26\">App\\Services\\ValueObjects\\ValidationResult::success<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#46\">App\\Services\\ValueObjects\\ValidationResult::failure<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#68\">App\\Services\\ValueObjects\\ValidationResult::getUrl<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#73\">App\\Services\\ValueObjects\\ValidationResult::getStatus<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#78\">App\\Services\\ValueObjects\\ValidationResult::getScope<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#83\">App\\Services\\ValueObjects\\ValidationResult::isValid<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#88\">App\\Services\\ValueObjects\\ValidationResult::isBroken<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#93\">App\\Services\\ValueObjects\\ValidationResult::isTemporary<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#98\">App\\Services\\ValueObjects\\ValidationResult::isSecurityIssue<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#103\">App\\Services\\ValueObjects\\ValidationResult::getError<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#108\">App\\Services\\ValueObjects\\ValidationResult::getHttpStatusCode<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#113\">App\\Services\\ValueObjects\\ValidationResult::getRedirectUrl<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#118\">App\\Services\\ValueObjects\\ValidationResult::getResponseTime<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#123\">App\\Services\\ValueObjects\\ValidationResult::getMetadata<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#131\">App\\Services\\ValueObjects\\ValidationResult::getSeverity<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#139\">App\\Services\\ValueObjects\\ValidationResult::getRecommendedAction<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#147\">App\\Services\\ValueObjects\\ValidationResult::getConsoleColor<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#155\">App\\Services\\ValueObjects\\ValidationResult::getFormattedDisplay<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#163\">App\\Services\\ValueObjects\\ValidationResult::shouldRetry<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#168\">App\\Services\\ValueObjects\\ValidationResult::toArray<\/a>"],[0,1,"<a href=\"ValueObjects\/ValidationResult.php.html#189\">App\\Services\\ValueObjects\\ValidationResult::toJson<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
