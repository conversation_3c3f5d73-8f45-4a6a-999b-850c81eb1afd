# Test File Analysis Report
Generated: 2025-07-25 13:21:25

## Summary
- Total files analyzed: 58
- Files with syntax errors: 0
- Files with mixed PHPUnit/Pest syntax: 0
- Files with embedded classes: 18

## Priority Issues

### Embedded Classes (Medium Priority)
- **tests/Support/Doubles/Commands/TestableBaseValidationCommand.php**
  - Embedded class/interface/trait found at line 18: class TestableBaseValidationCommand extends BaseValidationCommand

- **tests/Support/Doubles/Contracts/TestExtensionImplementation.php**
  - Embedded class/interface/trait found at line 12: class TestExtensionImplementation implements ExtensionInterface

- **tests/Support/Doubles/Contracts/TestGitHubAnchorImplementation.php**
  - Embedded class/interface/trait found at line 14: class TestGitHubAnchorImplementation implements GitHubAnchorInterface

- **tests/Support/Doubles/Contracts/TestLinkValidationImplementation.php**
  - Embedded class/interface/trait found at line 12: class TestLinkValidationImplementation implements LinkValidationInterface

- **tests/Support/Doubles/Contracts/TestReportingImplementation.php**
  - Embedded class/interface/trait found at line 13: class TestReportingImplementation implements ReportingInterface

- **tests/Support/Doubles/Contracts/TestSecurityValidationImplementation.php**
  - Embedded class/interface/trait found at line 14: class TestSecurityValidationImplementation implements SecurityValidationInterface

- **tests/Support/Doubles/Contracts/TestStatisticsImplementation.php**
  - Embedded class/interface/trait found at line 14: class TestStatisticsImplementation implements StatisticsInterface

- **tests/Support/Doubles/General/CommandTestHelpers.php**
  - Embedded class/interface/trait found at line 13: trait CommandTestHelpers

- **tests/Support/Doubles/General/EnumTestHelpers.php**
  - Embedded class/interface/trait found at line 11: trait EnumTestHelpers

- **tests/Support/Doubles/General/ExceptionTestHelpers.php**
  - Embedded class/interface/trait found at line 11: trait ExceptionTestHelpers

- **tests/Support/Doubles/General/FileTestHelpers.php**
  - Embedded class/interface/trait found at line 11: trait FileTestHelpers

- **tests/Support/Doubles/General/HttpTestHelpers.php**
  - Embedded class/interface/trait found at line 11: trait HttpTestHelpers

- **tests/Support/Doubles/General/ServiceTestHelpers.php**
  - Embedded class/interface/trait found at line 12: trait ServiceTestHelpers

- **tests/Support/Doubles/General/ValidationTestHelpers.php**
  - Embedded class/interface/trait found at line 12: trait ValidationTestHelpers

- **tests/Support/Doubles/General/ValueObjectTestHelpers.php**
  - Embedded class/interface/trait found at line 11: trait ValueObjectTestHelpers

- **tests/Traits/ExceptionTestHelpers.php**
  - Embedded class/interface/trait found at line 19: trait ExceptionTestHelpers

- **tests/Traits/ServiceTestHelpers.php**
  - Embedded class/interface/trait found at line 24: trait ServiceTestHelpers

- **tests/Traits/ValueObjectTestHelpers.php**
  - Embedded class/interface/trait found at line 19: trait ValueObjectTestHelpers

