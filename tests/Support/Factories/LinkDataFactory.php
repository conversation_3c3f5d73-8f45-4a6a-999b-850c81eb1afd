<?php

declare(strict_types=1);

namespace Tests\Support\Factories;

/**
 * Factory for creating link data structures for testing.
 *
 * Provides convenient methods for creating realistic link data
 * with different types and scenarios.
 */
class LinkDataFactory
{
    /**
     * Create a basic link data structure.
     */
    public static function create(array $overrides = []): array
    {
        $defaults = [
            'url' => 'https://example.com',
            'text' => 'Example Link',
            'title' => null,
            'line_number' => 1,
            'column' => 1,
            'file_path' => 'test.md',
            'type' => 'external',
            'anchor' => null,
        ];

        return array_merge($defaults, $overrides);
    }

    /**
     * Create an external HTTP link.
     */
    public static function external(array $overrides = []): array
    {
        return self::create(array_merge([
            'url' => 'https://external.example.com',
            'text' => 'External Link',
            'type' => 'external',
        ], $overrides));
    }

    /**
     * Create an internal relative link.
     */
    public static function internal(array $overrides = []): array
    {
        return self::create(array_merge([
            'url' => './internal-page.md',
            'text' => 'Internal Link',
            'type' => 'internal',
        ], $overrides));
    }

    /**
     * Create an anchor link.
     */
    public static function anchor(array $overrides = []): array
    {
        return self::create(array_merge([
            'url' => '#section-heading',
            'text' => 'Section Link',
            'type' => 'anchor',
            'anchor' => 'section-heading',
        ], $overrides));
    }

    /**
     * Create a mailto link.
     */
    public static function mailto(array $overrides = []): array
    {
        return self::create(array_merge([
            'url' => 'mailto:<EMAIL>',
            'text' => 'Email Link',
            'type' => 'mailto',
        ], $overrides));
    }

    /**
     * Create a file link.
     */
    public static function file(array $overrides = []): array
    {
        return self::create(array_merge([
            'url' => './assets/document.pdf',
            'text' => 'PDF Document',
            'type' => 'file',
        ], $overrides));
    }

    /**
     * Create an image link.
     */
    public static function image(array $overrides = []): array
    {
        return self::create(array_merge([
            'url' => './images/screenshot.png',
            'text' => 'Screenshot',
            'type' => 'image',
        ], $overrides));
    }

    /**
     * Create a link with specific line/column position.
     */
    public static function atPosition(int $line, int $column, array $overrides = []): array
    {
        return self::create(array_merge([
            'line_number' => $line,
            'column' => $column,
        ], $overrides));
    }

    /**
     * Create a link in a specific file.
     */
    public static function inFile(string $filePath, array $overrides = []): array
    {
        return self::create(array_merge([
            'file_path' => $filePath,
        ], $overrides));
    }

    /**
     * Create a collection of links for testing.
     */
    public static function collection(int $count = 5, array $overrides = []): array
    {
        $links = [];
        
        for ($i = 0; $i < $count; $i++) {
            $links[] = self::create(array_merge([
                'url' => "https://example.com/page-{$i}",
                'text' => "Link {$i}",
                'line_number' => $i + 1,
            ], $overrides));
        }

        return $links;
    }

    /**
     * Create a mixed collection of different link types.
     */
    public static function mixedCollection(): array
    {
        return [
            self::external(['url' => 'https://github.com', 'text' => 'GitHub']),
            self::internal(['url' => './readme.md', 'text' => 'README']),
            self::anchor(['url' => '#installation', 'text' => 'Installation']),
            self::mailto(['url' => 'mailto:<EMAIL>', 'text' => 'Support']),
            self::file(['url' => './docs/guide.pdf', 'text' => 'User Guide']),
            self::image(['url' => './assets/logo.png', 'text' => 'Logo']),
        ];
    }

    /**
     * Create broken link data (for testing error scenarios).
     */
    public static function broken(array $overrides = []): array
    {
        return self::create(array_merge([
            'url' => 'https://broken.example.com/404',
            'text' => 'Broken Link',
            'type' => 'external',
        ], $overrides));
    }

    /**
     * Create link data with title attribute.
     */
    public static function withTitle(string $title, array $overrides = []): array
    {
        return self::create(array_merge([
            'title' => $title,
        ], $overrides));
    }
}
