<?php

declare(strict_types=1);

namespace Tests\Support\Doubles\Contracts;

use App\Services\ValueObjects\ValidationConfig;

/**
 * Test implementation extracted from tests/Unit/Contracts/LinkValidationInterfaceTest.php
 */
class TestLinkValidationImplementation implements LinkValidationInterface
{
    public function validateLink(string $url, ValidationConfig $config): ValidationResult
    {
        return new ValidationResult($url, LinkStatus::VALID, ValidationScope::EXTERNAL);
    }

    public function validateLinks(array $urls, ValidationConfig $config): array
    {
        return array_map(fn($url) => $this->validateLink($url, $config), $urls);
    }

    public function validateFile(string $filePath, ValidationConfig $config): array
    {
        return [
            new ValidationResult('https://example.com', LinkStatus::VALID, ValidationScope::EXTERNAL)
        ];
    }

    public function extractLinks(string $content): array
    {
        return [
            ['url' => 'https://example.com', 'text' => 'Example', 'line' => 1]
        ];
    }

    public function isValidUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    public function normalizeUrl(string $url): string
    {
        return $url;
    }

    public function getSupportedScopes(): array
    {
        return [ValidationScope::EXTERNAL, ValidationScope::INTERNAL];
    }

    public function supportsScope(ValidationScope $scope): bool
    {
        return in_array($scope, $this->getSupportedScopes());
    }

    public function validateByScopes(array $scopes, array $targets, ValidationConfig $config): ValidationResult
    {
        return new ValidationResult('test', LinkStatus::VALID, ValidationScope::EXTERNAL);
    }

    public function getStatusStatistics(): array
    {
        return [LinkStatus::VALID => 1];
    }

    public function setTimeout(int $seconds): void
    {
        // Test implementation - no-op
    }
}