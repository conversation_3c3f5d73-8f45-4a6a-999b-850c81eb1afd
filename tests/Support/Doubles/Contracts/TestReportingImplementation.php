<?php

declare(strict_types=1);

namespace Tests\Support\Doubles\Contracts;

use App\Services\Contracts\ReportingInterface;
use App\Services\ValueObjects\ValidationConfig;

/**
 * Test implementation of ReportingInterface for unit testing.
 */
class TestReportingImplementation implements ReportingInterface
{
    public function generateReport(array $results, ValidationConfig $config, $command): int
    {
        $summary = $results['summary'] ?? [];
        $brokenLinks = $summary['broken_links'] ?? 0;

        return $brokenLinks > 0 ? 1 : 0;
    }

    public function generateSummary(array $results): array
    {
        $summary = $results['summary'] ?? [];

        return [
            'total' => $summary['total_links'] ?? 0,
            'valid' => $summary['valid_links'] ?? 0,
            'invalid' => $summary['broken_links'] ?? 0,
            'success_rate' => $this->calculateSuccessRate($summary),
            'execution_time' => $summary['execution_time'] ?? 0,
        ];
    }

    public function exportReport(array $results, string $outputPath, string $format): bool
    {
        // Simulate export operation
        $content = $this->formatResults($results, $format);

        // In a real implementation, this would write to file
        return !empty($content) && !empty($outputPath);
    }

    private function calculateSuccessRate(array $summary): float
    {
        $total = $summary['total_links'] ?? 0;
        $valid = $summary['valid_links'] ?? 0;

        return $total > 0 ? round(($valid / $total) * 100, 2) : 0.0;
    }

    private function formatResults(array $results, string $format): string
    {
        switch ($format) {
            case 'json':
                return json_encode($results, JSON_PRETTY_PRINT);
            case 'html':
                return '<html><body><h1>Validation Report</h1></body></html>';
            case 'markdown':
                return "# Validation Report\n\nResults summary...";
            case 'console':
                return "Validation Report\n================\n";
            default:
                return '';
        }
    }
}
