<?php

declare(strict_types=1);

namespace Tests\Support\Traits;

use App\Services\ValueObjects\ValidationConfig;

/**
 * Test implementation extracted from tests/Traits/ValidationTestHelpers.php
 */
trait ValidationTestHelpers
{
    protected function createValidationConfig(array $overrides = []): ValidationConfig
    {
        return ValidationConfig::withDefaults(array_merge([
            'scopes' => ['internal'],
            'timeout' => 5,
            'max_redirects' => 3,
            'concurrent_requests' => 1,
            'cache_enabled' => false,
        ], $overrides));
    }

    protected function createTestValidationResult(array $overrides = []): ValidationResult
    {
        return ValidationResult::success(
            files: $overrides['files'] ?? ['test.md'],
            links: $overrides['links'] ?? [['url' => 'test-link', 'status' => 'valid']],
            broken: $overrides['broken'] ?? [],
            statistics: $overrides['statistics'] ?? [
                'total_files' => 1,
                'total_links' => 1,
                'broken_links' => 0,
                'success_rate' => 100.0,
            ],
            duration: $overrides['duration'] ?? 1.0
        );
    }

    protected function createFailedValidationResult(array $brokenLinks = []): ValidationResult
    {
        $broken = $brokenLinks ?: [
            ['url' => 'broken-link.md', 'status' => 'not_found', 'error' => 'File not found'],
        ];

        return ValidationResult::failure(
            files: ['test.md'],
            links: [['url' => 'broken-link.md', 'status' => 'not_found']],
            broken: $broken,
            statistics: [
                'total_files' => 1,
                'total_links' => 1,
                'broken_links' => count($broken),
                'success_rate' => 0.0,
            ],
            duration: 1.0
        );
    }

    protected function assertValidationSuccess(ValidationResult $result): void
    {
        expect($result->isSuccess())->toBeTrue();
        expect($result->getBrokenLinks())->toBeEmpty();
    }

    protected function assertValidationFailure(ValidationResult $result, ?int $expectedBrokenCount = null): void
    {
        expect($result->isSuccess())->toBeFalse();

        if ($expectedBrokenCount !== null) {
            expect($result->getBrokenLinks())->toHaveCount($expectedBrokenCount);
        } else {
            expect($result->getBrokenLinks())->not->toBeEmpty();
        }
    }
}