<?php

declare(strict_types=1);

namespace Tests\Support\Traits;

use App\Enums\LinkStatus;
use Guz<PERSON><PERSON>ttp\Client;
use Guzzle<PERSON>ttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use <PERSON><PERSON><PERSON>;
use <PERSON><PERSON><PERSON>\MockInterface;

/**
 * Trait providing shared functionality for testing service classes.
 *
 * This trait follows the established preference for trait-based composition
 * over base class inheritance, providing reusable testing utilities for
 * all service test classes.
 *
 * All methods use pure Pest syntax with expect() statements.
 */
trait ServiceTestHelpers
{
    /**
     * Create a mock HTTP client with predefined responses.
     */
    protected function createMockHttpClient(array $responses = []): Client
    {
        $defaultResponses = [
            new Response(200, [], 'OK'),
        ];

        $mockHandler = new MockHandler($responses ?: $defaultResponses);
        $handlerStack = HandlerStack::create($mockHandler);

        return new Client(['handler' => $handlerStack]);
    }

    /**
     * Create HTTP responses for testing different scenarios.
     */
    protected function createHttpResponses(): array
    {
        return [
            'success' => new Response(200, [], 'OK'),
            'not_found' => new Response(404, [], 'Not Found'),
            'forbidden' => new Response(403, [], 'Forbidden'),
            'timeout' => new Response(408, [], 'Request Timeout'),
            'server_error' => new Response(500, [], 'Internal Server Error'),
            'redirect' => new Response(301, ['Location' => 'https://example.com/new'], ''),
        ];
    }

    /**
     * Create a sample link validation result.
     */
    protected function createLinkValidationResult(
        string $url = 'https://example.com',
        LinkStatus $status = LinkStatus::VALID,
        array $metadata = []
    ): array {
        return array_merge([
            'url' => $url,
            'status' => $status->value,
            'status_code' => $status->getHttpStatusCode(),
            'response_time' => 150,
            'final_url' => $url,
            'redirect_count' => 0,
            'error_message' => null,
            'checked_at' => now()->toISOString(),
        ], $metadata);
    }

    /**
     * Create multiple link validation results for testing.
     */
    protected function createMultipleLinkResults(int $count = 5): array
    {
        $results = [];
        $statuses = [LinkStatus::VALID, LinkStatus::BROKEN, LinkStatus::NOT_FOUND];

        for ($i = 0; $i < $count; $i++) {
            $status = $statuses[$i % count($statuses)];
            $results[] = $this->createLinkValidationResult(
                "https://example{$i}.com",
                $status
            );
        }

        return $results;
    }

    /**
     * Create a validation summary for testing.
     */
    protected function createValidationSummary(array $overrides = []): array
    {
        return array_merge([
            'total_links' => 10,
            'valid_links' => 8,
            'broken_links' => 2,
            'files_processed' => 3,
            'execution_time' => 2.5,
            'success_rate' => 80.0,
            'average_response_time' => 200,
        ], $overrides);
    }

    /**
     * Create test markdown content with various link types.
     */
    protected function createTestMarkdownContent(): string
    {
        return <<<'MARKDOWN'
# Test Document

This is a test document with various link types.

## Internal Links
- [Internal file](./internal.md)
- [Another internal file](../docs/guide.md)

## External Links
- [External website](https://example.com)
- [Another external site](https://github.com/user/repo)

## Anchor Links
- [Section anchor](#section-1)
- [Another anchor](#section-2)

## Image Links
![Test image](./images/test.png)
![External image](https://example.com/image.jpg)

## Section 1
Content for section 1.

## Section 2
Content for section 2.
MARKDOWN;
    }

    /**
     * Create test HTML content with various link types.
     */
    protected function createTestHtmlContent(): string
    {
        return <<<'HTML'
<!DOCTYPE html>
<html>
<head>
    <title>Test Document</title>
</head>
<body>
    <h1>Test Document</h1>

    <h2>Links</h2>
    <ul>
        <li><a href="./internal.html">Internal link</a></li>
        <li><a href="https://example.com">External link</a></li>
        <li><a href="#section1">Anchor link</a></li>
    </ul>

    <h2>Images</h2>
    <img src="./images/test.png" alt="Test image">
    <img src="https://example.com/image.jpg" alt="External image">

    <h2 id="section1">Section 1</h2>
    <p>Content for section 1.</p>
</body>
</html>
HTML;
    }

    /**
     * Assert that validation results contain expected structure.
     */
    protected function assertValidationResultStructure(array $result): void
    {
        expect($result)->toHaveKey('url');
        expect($result)->toHaveKey('status');
        expect($result)->toHaveKey('status_code');
        expect($result)->toHaveKey('response_time');
        expect($result)->toHaveKey('checked_at');
    }

    /**
     * Assert that validation summary contains expected structure.
     */
    protected function assertValidationSummaryStructure(array $summary): void
    {
        $requiredKeys = [
            'total_links',
            'valid_links',
            'broken_links',
            'files_processed',
            'execution_time',
            'success_rate',
        ];

        foreach ($requiredKeys as $key) {
            expect($summary)->toHaveKey($key);
        }
    }

    /**
     * Create a mock cache for testing caching functionality.
     */
    protected function createMockCache(): MockInterface
    {
        return Mockery::mock('\Illuminate\Contracts\Cache\Repository');
    }

    /**
     * Set up cache expectations for testing.
     */
    protected function expectCacheOperations(MockInterface $cache, array $operations = []): void
    {
        foreach ($operations as $operation => $config) {
            switch ($operation) {
                case 'get':
                    $cache->shouldReceive('get')
                        ->with($config['key'])
                        ->andReturn($config['value'] ?? null);
                    break;
                case 'put':
                    $cache->shouldReceive('put')
                        ->with($config['key'], $config['value'], $config['ttl'] ?? 3600)
                        ->once();
                    break;
                case 'forget':
                    $cache->shouldReceive('forget')
                        ->with($config['key'])
                        ->once();
                    break;
            }
        }
    }

    /**
     * Create test configuration for services.
     */
    protected function createServiceTestConfig(array $overrides = []): array
    {
        return array_merge([
            'timeout' => 10,
            'max_redirects' => 3,
            'concurrent_requests' => 5,
            'cache_enabled' => false,
            'user_agent' => 'test-agent/1.0',
        ], $overrides);
    }

    /**
     * Assert that service method was called with expected parameters.
     */
    protected function assertServiceMethodCalled(
        MockInterface $mock,
        string $method,
        array $expectedParams = [],
        int $times = 1
    ): void {
        // Suppress unused parameter warnings for simplified implementation
        unset($expectedParams, $times);

        // Verify the method was called
        $mock->shouldHaveReceived($method);

        // Note: For more complex assertions, use Mockery expectations directly in tests
        // This is a simplified helper for basic verification
    }

    /**
     * Create test file paths for service testing.
     */
    protected function createTestFilePaths(): array
    {
        return [
            'markdown' => $this->getTestPath('test.md'),
            'html' => $this->getTestPath('test.html'),
            'directory' => $this->getTestPath('docs/'),
        ];
    }

    /**
     * Get test path helper method.
     */
    protected function getTestPath(string $relativePath): string
    {
        return storage_path("framework/testing/{$relativePath}");
    }

    /**
     * Set up service test environment.
     */
    protected function setUpServiceTest(): void
    {
        // Configure test environment for services
        config(['validate-links.cache.enabled' => false]);
        config(['validate-links.external.timeout' => 5]);
        config(['validate-links.external.max_redirects' => 3]);
    }

    /**
     * Tear down service test environment.
     */
    protected function tearDownServiceTest(): void
    {
        Mockery::close();
    }
}
