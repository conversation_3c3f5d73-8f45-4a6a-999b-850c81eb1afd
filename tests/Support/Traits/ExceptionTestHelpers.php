<?php

declare(strict_types=1);

namespace Tests\Support\Traits;


/**
 * Test implementation extracted from tests/Traits/ExceptionTestHelpers.php
 */
trait ExceptionTestHelpers
{
    /**
     * Assert that an exception has the expected structure and methods.
     */
    protected function assertExceptionStructure(ValidateLinksException $exception): void
    {
        $this->assertIsString($exception->getErrorCode());
        $this->assertIsString($exception->getSeverity());
        $this->assertIsArray($exception->getContext());
        $this->assertIsArray($exception->toArray());
    }

    /**
     * Assert that exception toArray method returns expected structure.
     */
    protected function assertExceptionArrayStructure(array $exceptionArray): void
    {
        $requiredKeys = [
            'error_code',
            'message',
            'severity',
            'file',
            'line',
            'context',
        ];

        foreach ($requiredKeys as $key) {
            $this->assertArrayHasKey($key, $exceptionArray);
        }
    }

    /**
     * Assert that exception has expected error code.
     */
    protected function assertExceptionErrorCode(
        ValidateLinksException $exception,
        string $expectedCode
    ): void {
        $this->assertEquals($expectedCode, $exception->getErrorCode());
    }

    /**
     * Assert that exception has expected severity level.
     */
    protected function assertExceptionSeverity(
        ValidateLinksException $exception,
        string $expectedSeverity
    ): void {
        $this->assertEquals($expectedSeverity, $exception->getSeverity());
    }

    /**
     * Assert that exception message contains expected text.
     */
    protected function assertExceptionMessageContains(
        Throwable $exception,
        string $expectedText
    ): void {
        $this->assertStringContainsString($expectedText, $exception->getMessage());
    }

    /**
     * Assert that exception was thrown with expected message.
     */
    protected function assertExceptionThrownWithMessage(
        string $exceptionClass,
        string $expectedMessage,
        callable $callback
    ): void {
        $this->expectException($exceptionClass);
        $this->expectExceptionMessage($expectedMessage);
        
        $callback();
    }

    /**
     * Assert that exception was thrown with expected error code.
     */
    protected function assertExceptionThrownWithErrorCode(
        string $exceptionClass,
        string $expectedErrorCode,
        callable $callback
    ): void {
        try {
            $callback();
            $this->fail("Expected exception {$exceptionClass} was not thrown");
        } catch (ValidateLinksException $e) {
            $this->assertInstanceOf($exceptionClass, $e);
            $this->assertEquals($expectedErrorCode, $e->getErrorCode());
        }
    }

    /**
     * Create test exception with specified parameters.
     */
    protected function createTestException(
        string $exceptionClass,
        string $message = 'Test exception',
        int $code = 0,
        ?Throwable $previous = null
    ): ValidateLinksException {
        return new $exceptionClass($message, $code, $previous);
    }

    /**
     * Test exception inheritance chain.
     */
    protected function assertExceptionInheritance(
        string $exceptionClass,
        string $expectedParentClass
    ): void {
        $this->assertTrue(
            is_subclass_of($exceptionClass, $expectedParentClass),
            "{$exceptionClass} should extend {$expectedParentClass}"
        );
    }

    /**
     * Test that exception implements required methods.
     */
    protected function assertExceptionImplementsRequiredMethods(
        ValidateLinksException $exception
    ): void {
        $requiredMethods = ['getErrorCode', 'getSeverity', 'getContext', 'toArray'];
        
        foreach ($requiredMethods as $method) {
            $this->assertTrue(
                method_exists($exception, $method),
                "Exception should implement {$method} method"
            );
        }
    }

    /**
     * Test exception context data.
     */
    protected function assertExceptionContext(
        ValidateLinksException $exception,
        array $expectedContext = []
    ): void {
        $context = $exception->getContext();
        
        foreach ($expectedContext as $key => $value) {
            $this->assertArrayHasKey($key, $context);
            $this->assertEquals($value, $context[$key]);
        }
    }

    /**
     * Test exception serialization.
     */
    protected function assertExceptionSerializable(ValidateLinksException $exception): void
    {
        $serialized = serialize($exception);
        $unserialized = unserialize($serialized);
        
        $this->assertInstanceOf(get_class($exception), $unserialized);
        $this->assertEquals($exception->getMessage(), $unserialized->getMessage());
        $this->assertEquals($exception->getCode(), $unserialized->getCode());
    }

    /**
     * Test exception JSON serialization.
     */
    protected function assertExceptionJsonSerializable(ValidateLinksException $exception): void
    {
        $array = $exception->toArray();
        $json = json_encode($array);
        $decoded = json_decode($json, true);
        
        $this->assertIsString($json);
        $this->assertIsArray($decoded);
        $this->assertEquals($array, $decoded);
    }

    /**
     * Create exception test scenarios.
     */
    protected function createExceptionTestScenarios(): array
    {
        return [
            'basic_exception' => [
                'message' => 'Basic test exception',
                'code' => 100,
                'previous' => null,
            ],
            'exception_with_previous' => [
                'message' => 'Exception with previous',
                'code' => 200,
                'previous' => new \Exception('Previous exception'),
            ],
            'exception_with_context' => [
                'message' => 'Exception with context',
                'code' => 300,
                'previous' => null,
                'context' => ['key' => 'value', 'data' => ['nested' => 'value']],
            ],
        ];
    }

    /**
     * Test exception with different severity levels.
     */
    protected function assertExceptionSeverityLevels(string $exceptionClass): void
    {
        $validSeverities = ['low', 'medium', 'high', 'critical'];
        
        foreach ($validSeverities as $severity) {
            $exception = $this->createTestException($exceptionClass);
            
            // This would need to be implemented in specific exception classes
            // to test different severity scenarios
            $this->assertContains(
                $exception->getSeverity(),
                $validSeverities,
                "Exception severity should be one of the valid levels"
            );
        }
    }

    /**
     * Test exception error code format.
     */
    protected function assertExceptionErrorCodeFormat(
        ValidateLinksException $exception,
        string $expectedPattern = '/^[A-Z_]+$/'
    ): void {
        $errorCode = $exception->getErrorCode();
        
        $this->assertMatchesRegularExpression(
            $expectedPattern,
            $errorCode,
            "Error code should match expected format"
        );
    }

    /**
     * Set up exception test environment.
     */
    protected function setUpExceptionTest(): void
    {
        // Configure error reporting for exception tests
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
    }

    /**
     * Tear down exception test environment.
     */
    protected function tearDownExceptionTest(): void
    {
        // Clean up any exception-related state
    }
}