<?php

declare(strict_types=1);

namespace Tests\Services\ValueObjects;

use App\Services\ValueObjects\ValidationConfig;
use App\Enums\ValidationScope;
use App\Enums\OutputFormat;
use Tests\Support\Traits\ValueObjectTestHelpers;

uses(ValueObjectTestHelpers::class);

describe('ValidationConfig', function () {
    beforeEach(function () {
        $this->setUpValueObjectTest();
    });

    afterEach(function () {
        $this->tearDownValueObjectTest();
    });

    test('construct creates valid instance', function () {
        $config = new ValidationConfig([
            'timeout' => 30,
            'concurrent_requests' => 5,
            'scopes' => [ValidationScope::INTERNAL],
        ]);

        expect($config)->toBeInstanceOf(ValidationConfig::class);
        expect($config->getTimeout())->toBe(30);
        expect($config->getConcurrentRequests())->toBe(5);
        expect($config->getScopes())->toContain(ValidationScope::INTERNAL);
    });

    test('create factory method works', function () {
        $data = [
            'timeout' => 60,
            'concurrent_requests' => 10,
            'scopes' => [ValidationScope::EXTERNAL],
        ];

        $config = ValidationConfig::create($data);

        expect($config)->toBeInstanceOf(ValidationConfig::class);

        // Assert immutability
        $reflection = new \ReflectionClass($config);
        $properties = $reflection->getProperties();
        foreach ($properties as $property) {
            expect(
                $property->isReadOnly() || $property->isPrivate() || $property->isProtected()
            )->toBeTrue("Property {$property->getName()} should be readonly or private/protected for immutability");
        }
    });

    test('has expected constructor parameters', function () {
        $reflection = new \ReflectionClass(ValidationConfig::class);
        $constructor = $reflection->getConstructor();

        expect($constructor)->not->toBeNull("Value object should have a constructor");

        $parameters = $constructor->getParameters();
        $parameterNames = array_map(fn($param) => $param->getName(), $parameters);

        $expectedParameters = ['timeout', 'concurrent_requests', 'scopes'];
        foreach ($expectedParameters as $expectedParam) {
            expect($parameterNames)->toContain(
                $expectedParam,
                "Constructor should have parameter: {$expectedParam}"
            );
        }
    });

    test('has expected getter methods', function () {
        $config = ValidationConfig::create([
            'timeout' => 30,
            'concurrent_requests' => 5,
        ]);

        $reflection = new \ReflectionClass($config);
        $expectedGetters = [
            'getTimeout',
            'getConcurrentRequests',
            'getScopes',
            'getOutputFormat',
            'shouldValidateInternal',
            'shouldValidateExternal',
            'shouldFollowRedirects',
        ];

        foreach ($expectedGetters as $getter) {
            expect($reflection->hasMethod($getter))->toBeTrue(
                "Value object should have getter method: {$getter}"
            );

            $method = $reflection->getMethod($getter);
            expect($method->isPublic())->toBeTrue(
                "Getter method {$getter} should be public"
            );
        }
    });

    test('to array returns expected structure', function () {
        $config = ValidationConfig::create([
            'timeout' => 30,
            'concurrent_requests' => 5,
            'scopes' => [ValidationScope::INTERNAL],
        ]);

        expect(method_exists($config, 'toArray'))->toBeTrue(
            "Value object should have toArray method"
        );

        $array = $config->toArray();
        expect($array)->toBeArray();

        $expectedKeys = ['timeout', 'concurrent_requests', 'scopes', 'output_format'];
        foreach ($expectedKeys as $key) {
            expect($array)->toHaveKey($key);
        }
    });

    test('is serializable', function () {
        $config = ValidationConfig::create([
            'timeout' => 30,
            'concurrent_requests' => 5,
        ]);

        $serialized = serialize($config);
        $unserialized = unserialize($serialized);

        expect($unserialized)->toBeInstanceOf(get_class($config));
        expect($unserialized)->toEqual($config);
    });

    test('validates input data', function () {
        $invalidData = [
            'timeout' => -1,
            'concurrent_requests' => 0,
        ];

        expect(fn() => ValidationConfig::create($invalidData))
            ->toThrow(\InvalidArgumentException::class);
    });

    test('equality works correctly', function () {
        $config1 = ValidationConfig::create(['timeout' => 30]);
        $config2 = ValidationConfig::create(['timeout' => 30]);
        $config3 = ValidationConfig::create(['timeout' => 60]);

        expect($config1)->toEqual($config2);
        expect($config1)->not->toEqual($config3);
    });

    test('has scope method works', function () {
        $config = ValidationConfig::create([
            'scopes' => [ValidationScope::INTERNAL, ValidationScope::EXTERNAL],
        ]);

        expect($config->hasScope(ValidationScope::INTERNAL))->toBeTrue();
        expect($config->hasScope(ValidationScope::ANCHOR))->toBeFalse();
    });

    test('get effective scopes returns correct scopes', function () {
        $config = ValidationConfig::create([
            'scopes' => [ValidationScope::INTERNAL],
        ]);

        $effectiveScopes = $config->getEffectiveScopes();
        expect($effectiveScopes)->toBeArray();
        expect($effectiveScopes)->toContain(ValidationScope::INTERNAL);
    });

    test('with scopes creates new instance', function () {
        $original = ValidationConfig::create(['timeout' => 30]);
        $modified = $original->withScopes([ValidationScope::EXTERNAL]);

        expect($modified)->not->toBe($original);
        expect($modified->hasScope(ValidationScope::EXTERNAL))->toBeTrue();
        expect($original->hasScope(ValidationScope::EXTERNAL))->toBeFalse();
    });

    test('with output format creates new instance', function () {
        $original = ValidationConfig::create(['timeout' => 30]);
        $modified = $original->withOutputFormat(OutputFormat::JSON);

        expect($modified)->not->toBe($original);
        expect($modified->getOutputFormat())->toBe(OutputFormat::JSON);
    });

    test('with defaults applies default values', function () {
        $config = ValidationConfig::withDefaults();

        expect($config->getTimeout())->toBeGreaterThan(0);
        expect($config->getConcurrentRequests())->toBeGreaterThan(0);
        expect($config->getScopes())->not->toBeEmpty();
    });
});
