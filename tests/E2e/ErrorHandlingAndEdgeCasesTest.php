<?php

declare(strict_types=1);

namespace Tests\E2e;

use Tests\Traits\FileTestHelpers;
use Tests\Traits\HttpTestHelpers;
use Tests\Traits\ValidationTestHelpers;

uses(FileTestHelpers::class, HttpTestHelpers::class, ValidationTestHelpers::class);

describe('Error Handling and Edge Cases', function () {
    beforeEach(function () {
        $this->setUpFeatureTest();
    });

    afterEach(function () {
        $this->tearDownFeatureTest();
    });

    describe('invalid file paths and permissions', function () {
        test('handles non-existent file paths gracefully', function () {
            $this->artisan('validate', ['path' => '/completely/non/existent/path'])
                ->expectsOutput('❌ Path not found: /completely/non/existent/path')
                ->expectsOutput('Please check the path and try again')
                ->assertExitCode(1);
        });

        test('handles permission denied errors', function () {
            $restrictedDir = $this->createTestDirectory('restricted');
            chmod($restrictedDir, 0000); // No permissions

            $this->artisan('validate', ['path' => $restrictedDir])
                ->expectsOutput('❌ Permission denied: ' . $restrictedDir)
                ->expectsOutput('Cannot access directory')
                ->assertExitCode(1);

            // Restore permissions for cleanup
            chmod($restrictedDir, 0755);
        });

        test('handles broken symbolic links', function () {
            // Create symbolic link to non-existent target
            symlink('/non/existent/target', 'broken-link');

            $this->artisan('validate', [
                'path' => 'broken-link',
                '--follow-symlinks' => true
            ])
                ->expectsOutput('❌ Broken symbolic link: broken-link')
                ->expectsOutput('Target does not exist: /non/existent/target')
                ->assertExitCode(1);
        });

        test('handles circular symbolic links', function () {
            // Create circular symbolic links
            symlink('link2', 'link1');
            symlink('link1', 'link2');

            $this->artisan('validate', [
                'path' => 'link1',
                '--follow-symlinks' => true
            ])
                ->expectsOutput('❌ Circular symbolic link detected: link1')
                ->expectsOutput('Stopping to prevent infinite loop')
                ->assertExitCode(1);
        });
    });

    describe('malformed configuration files', function () {
        test('handles invalid JSON in configuration file', function () {
            file_put_contents('validate-links.json', '{ "scope": ["external", }'); // Invalid JSON

            $this->artisan('validate', ['path' => '.'])
                ->expectsOutput('❌ Configuration file contains invalid JSON')
                ->expectsOutput('Error: Syntax error in validate-links.json')
                ->expectsOutput('Please fix the configuration file or recreate it')
                ->assertExitCode(1);
        });

        test('handles missing required configuration fields', function () {
            $this->createConfigFile(['invalid_field' => 'value']); // Missing required fields

            $this->artisan('validate', ['path' => '.'])
                ->expectsOutput('⚠️  Configuration file missing required fields')
                ->expectsOutput('Using default values for missing fields')
                ->assertExitCode(0);
        });

        test('handles configuration with invalid data types', function () {
            $this->createConfigFile([
                'scope' => 'should-be-array',
                'timeout' => 'should-be-number',
                'concurrent' => 'should-be-boolean'
            ]);

            $this->artisan('validate', ['path' => '.'])
                ->expectsOutput('❌ Configuration validation failed')
                ->expectsOutput('Invalid data type for scope: expected array, got string')
                ->expectsOutput('Invalid data type for timeout: expected number, got string')
                ->expectsOutput('Invalid data type for concurrent: expected boolean, got string')
                ->assertExitCode(1);
        });

        test('handles configuration file with excessive nesting', function () {
            $deepConfig = ['level1' => ['level2' => ['level3' => ['level4' => ['level5' => 'too-deep']]]]];
            $this->createConfigFile($deepConfig);

            $this->artisan('config', ['--validate' => true])
                ->expectsOutput('⚠️  Configuration has excessive nesting')
                ->expectsOutput('Consider flattening the configuration structure')
                ->assertExitCode(0);
        });
    });

    describe('network connectivity issues', function () {
        test('handles DNS resolution failures', function () {
            $this->createTestFile('dns-test.md', '[Broken DNS](https://definitely-does-not-exist-12345.invalid)');

            $this->artisan('validate', ['path' => 'dns-test.md'])
                ->expectsOutput('🔍 Validating links in: dns-test.md')
                ->expectsOutput('❌ Found 1 broken link')
                ->expectsOutput('DNS resolution failed: definitely-does-not-exist-12345.invalid')
                ->assertExitCode(1);
        });

        test('handles connection timeouts gracefully', function () {
            $this->createTestFile('timeout-test.md', '[Slow Link](https://httpbin.org/delay/30)');

            $this->artisan('validate', [
                'path' => 'timeout-test.md',
                '--timeout' => 2
            ])
                ->expectsOutput('🔍 Validating links in: timeout-test.md')
                ->expectsOutput('❌ Found 1 broken link')
                ->expectsOutput('Connection timeout after 2 seconds')
                ->assertExitCode(1);
        });

        test('handles SSL certificate errors', function () {
            $this->createTestFile('ssl-test.md', '[SSL Error](https://self-signed.badssl.com)');

            $this->artisan('validate', ['path' => 'ssl-test.md'])
                ->expectsOutput('🔍 Validating links in: ssl-test.md')
                ->expectsOutput('❌ Found 1 broken link')
                ->expectsOutput('SSL certificate error')
                ->assertExitCode(1);
        });

        test('bypasses SSL verification when configured', function () {
            $this->createTestFile('ssl-bypass-test.md', '[SSL Bypass](https://self-signed.badssl.com)');

            $this->artisan('validate', [
                'path' => 'ssl-bypass-test.md',
                '--ignore-ssl-errors' => true
            ])
                ->expectsOutput('⚠️  SSL verification disabled')
                ->expectsOutput('🔍 Validating links in: ssl-bypass-test.md')
                ->expectsOutput('✅ All links are valid!')
                ->assertExitCode(0);
        });

        test('handles rate limiting from external services', function () {
            $this->createTestFile('rate-limit-test.md', '
[Rate Limited 1](https://httpbin.org/status/429)
[Rate Limited 2](https://httpbin.org/status/429)
[Rate Limited 3](https://httpbin.org/status/429)
            ');

            $this->artisan('validate', [
                'path' => 'rate-limit-test.md',
                '--respect-rate-limits' => true
            ])
                ->expectsOutput('⚠️  Rate limiting detected, slowing down requests')
                ->expectsOutput('🔍 Validating links in: rate-limit-test.md')
                ->expectsOutput('❌ Found 3 broken links')
                ->expectsOutput('Rate limited (429 Too Many Requests)')
                ->assertExitCode(1);
        });
    });

    describe('invalid command arguments', function () {
        test('handles invalid scope values', function () {
            $this->artisan('validate', [
                'path' => '.',
                '--scope' => 'invalid-scope'
            ])
                ->expectsOutput('❌ Invalid scope: invalid-scope')
                ->expectsOutput('Valid scopes: internal, external, anchor, all')
                ->expectsOutput('Example: --scope external,internal')
                ->assertExitCode(1);
        });

        test('handles invalid format values', function () {
            $this->artisan('validate', [
                'path' => '.',
                '--format' => 'invalid-format'
            ])
                ->expectsOutput('❌ Invalid format: invalid-format')
                ->expectsOutput('Valid formats: console, json, html')
                ->assertExitCode(1);
        });

        test('handles invalid timeout values', function () {
            $this->artisan('validate', [
                'path' => '.',
                '--timeout' => '-5'
            ])
                ->expectsOutput('❌ Invalid timeout value: -5')
                ->expectsOutput('Timeout must be a positive integer')
                ->assertExitCode(1);
        });

        test('handles conflicting command options', function () {
            $this->artisan('validate', [
                'path' => '.',
                '--concurrent' => true,
                '--concurrent-requests' => '0'
            ])
                ->expectsOutput('❌ Conflicting options: concurrent enabled but concurrent-requests is 0')
                ->expectsOutput('Set concurrent-requests to a positive value or disable concurrent mode')
                ->assertExitCode(1);
        });

        test('handles missing required arguments', function () {
            $this->artisan('fix')
                ->expectsOutput('❌ Missing required argument: paths')
                ->expectsOutput('Usage: fix <paths...> [options]')
                ->assertExitCode(1);
        });
    });

    describe('malformed content and parsing errors', function () {
        test('handles files with malformed markdown', function () {
            $malformedContent = '# Title\n[Broken link syntax](missing-closing-paren\n[Another](valid.md)';
            $this->createTestFile('malformed.md', $malformedContent);

            $this->artisan('validate', ['path' => 'malformed.md'])
                ->expectsOutput('⚠️  Malformed markdown detected in: malformed.md')
                ->expectsOutput('Line 2: Unclosed link syntax')
                ->expectsOutput('📊 Found 1 valid link to validate')
                ->assertExitCode(0);
        });

        test('handles files with invalid HTML', function () {
            $invalidHtml = '<html><body><a href="https://example.com">Link</a><unclosed-tag></body>';
            $this->createTestFile('invalid.html', $invalidHtml);

            $this->artisan('validate', ['path' => 'invalid.html'])
                ->expectsOutput('⚠️  Invalid HTML detected in: invalid.html')
                ->expectsOutput('Attempting to parse with error recovery')
                ->expectsOutput('📊 Found 1 link to validate')
                ->assertExitCode(0);
        });

        test('handles files with mixed line endings', function () {
            $mixedContent = "# Title\r\n[Link 1](https://example.com)\n[Link 2](https://github.com)\r\n";
            $this->createTestFile('mixed-endings.md', $mixedContent);

            $this->artisan('validate', ['path' => 'mixed-endings.md'])
                ->expectsOutput('ℹ️  Mixed line endings detected, normalizing')
                ->expectsOutput('📊 Found 2 links to validate')
                ->assertExitCode(0);
        });

        test('handles extremely long URLs', function () {
            $longUrl = 'https://example.com/' . str_repeat('very-long-path/', 100);
            $this->createTestFile('long-url.md', "[Long URL]({$longUrl})");

            $this->artisan('validate', ['path' => 'long-url.md'])
                ->expectsOutput('⚠️  Extremely long URL detected (>2000 characters)')
                ->expectsOutput('📊 Found 1 link to validate')
                ->assertExitCode(0);
        });

        test('handles URLs with special characters', function () {
            $specialContent = '
[Unicode URL](https://example.com/测试)
[Encoded URL](https://example.com/test%20space)
[Query String](https://example.com/search?q=test&lang=en)
[Fragment](https://example.com/page#section-1)
            ';
            $this->createTestFile('special-chars.md', $specialContent);

            $this->artisan('validate', ['path' => 'special-chars.md'])
                ->expectsOutput('🌍 Processing URLs with international characters')
                ->expectsOutput('📊 Found 4 links to validate')
                ->assertExitCode(0);
        });
    });

    describe('resource exhaustion and limits', function () {
        test('handles memory exhaustion gracefully', function () {
            // Simulate memory pressure by creating many large files
            $this->createTestDirectory('memory-test');
            for ($i = 1; $i <= 10; $i++) {
                $largeContent = str_repeat("# Large Content\n[Link](https://example.com)\n", 1000);
                $this->createTestFile("memory-test/large{$i}.md", $largeContent);
            }

            $this->artisan('validate', [
                'path' => 'memory-test',
                '--memory-limit' => '64M'
            ])
                ->expectsOutput('💾 Memory limit set to: 64M')
                ->expectsOutput('💾 Using streaming mode to conserve memory')
                ->expectsOutput('📁 Processing 10 files')
                ->assertExitCode(0);
        });

        test('handles too many open files error', function () {
            // Create many files to potentially hit file descriptor limits
            $this->createTestDirectory('many-files');
            for ($i = 1; $i <= 100; $i++) {
                $this->createTestFile("many-files/file{$i}.md", "[Link {$i}](https://example.com/{$i})");
            }

            $this->artisan('validate', [
                'path' => 'many-files',
                '--concurrent' => true,
                '--concurrent-requests' => 50
            ])
                ->expectsOutput('⚠️  High concurrency detected, monitoring file descriptors')
                ->expectsOutput('📁 Processing 100 files')
                ->assertExitCode(0);
        });

        test('handles disk space exhaustion during report generation', function () {
            $this->createTestFile('disk-test.md', '[Link](https://example.com)');

            // Mock disk space check
            $this->artisan('report', [
                'paths' => ['disk-test.md'],
                '--check-disk-space' => true,
                '--min-free-space' => '1GB'
            ])
                ->expectsOutput('💾 Checking available disk space')
                ->expectsOutput('📊 Generating link validation report')
                ->assertExitCode(0);
        });
    });

    describe('concurrent processing edge cases', function () {
        test('handles race conditions in concurrent validation', function () {
            $this->createTestFile('race-test.md', '
[Link 1](https://httpbin.org/delay/1)
[Link 2](https://httpbin.org/delay/1)
[Link 3](https://httpbin.org/delay/1)
            ');

            $this->artisan('validate', [
                'path' => 'race-test.md',
                '--concurrent' => true,
                '--concurrent-requests' => 3
            ])
                ->expectsOutput('⚡ Using concurrent validation (3 requests)')
                ->expectsOutput('📊 Found 3 links to validate')
                ->expectsOutput('✅ All links are valid!')
                ->assertExitCode(0);
        });

        test('handles worker process failures in concurrent mode', function () {
            $this->createTestFile('worker-test.md', '
[Good Link](https://example.com)
[Bad Link](https://nonexistent-domain-12345.com)
[Another Good](https://github.com)
            ');

            $this->artisan('validate', [
                'path' => 'worker-test.md',
                '--concurrent' => true,
                '--concurrent-requests' => 2,
                '--retry-failed' => true
            ])
                ->expectsOutput('⚡ Using concurrent validation (2 requests)')
                ->expectsOutput('🔄 Retrying failed validations')
                ->expectsOutput('❌ Found 1 broken link')
                ->assertExitCode(1);
        });

        test('handles deadlock prevention in concurrent processing', function () {
            // Create interdependent files that could cause deadlocks
            $this->createTestFile('file1.md', '[Link to file2](./file2.md)');
            $this->createTestFile('file2.md', '[Link to file1](./file1.md)');

            $this->artisan('validate', [
                'path' => '.',
                '--concurrent' => true,
                '--deadlock-detection' => true
            ])
                ->expectsOutput('🔒 Deadlock detection enabled')
                ->expectsOutput('📊 Found 2 internal links to validate')
                ->expectsOutput('✅ All links are valid!')
                ->assertExitCode(0);
        });
    });

    describe('graceful degradation', function () {
        test('continues processing when some files fail', function () {
            $this->createTestFile('good.md', '[Good Link](https://example.com)');
            $corruptFile = $this->createTestFile('corrupt.md', '[Link](https://example.com)');

            // Corrupt the file
            file_put_contents($corruptFile, "\x00\x01\x02\x03"); // Binary data

            $this->artisan('validate', [
                'path' => '.',
                '--continue-on-error' => true
            ])
                ->expectsOutput('❌ Failed to process: corrupt.md (binary file detected)')
                ->expectsOutput('✅ Continuing with remaining files')
                ->expectsOutput('📊 Found 1 link to validate')
                ->assertExitCode(0);
        });

        test('provides partial results when network is unreliable', function () {
            $this->createTestFile('mixed-network.md', '
[Local Link](./good.md)
[Unreachable](https://definitely-unreachable-12345.com)
[Another Local](./also-good.md)
            ');

            $this->createTestFile('good.md', '# Good');
            $this->createTestFile('also-good.md', '# Also Good');

            $this->artisan('validate', [
                'path' => 'mixed-network.md',
                '--partial-results' => true
            ])
                ->expectsOutput('🔍 Validating links in: mixed-network.md')
                ->expectsOutput('✅ Internal links: 2/2 valid')
                ->expectsOutput('❌ External links: 0/1 valid')
                ->expectsOutput('📊 Partial validation completed')
                ->assertExitCode(1);
        });

        test('falls back to basic validation when advanced features fail', function () {
            $this->createTestFile('fallback-test.md', '[Link](https://example.com)');

            $this->artisan('validate', [
                'path' => 'fallback-test.md',
                '--advanced-parsing' => true,
                '--fallback-on-error' => true
            ])
                ->expectsOutput('⚠️  Advanced parsing failed, falling back to basic mode')
                ->expectsOutput('📊 Found 1 link to validate')
                ->expectsOutput('✅ All links are valid!')
                ->assertExitCode(0);
        });
    });
});
