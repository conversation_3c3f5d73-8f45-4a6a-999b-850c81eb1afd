<?php

declare(strict_types=1);

namespace Tests\Unit\Exceptions;

use App\Exceptions\ValidateLinksException;

describe('ValidateLinksException', function () {
    beforeEach(function () {
        // Exception test setup - no longer needed in pure Pest syntax
    });

    afterEach(function () {
        // Exception test teardown - no longer needed in pure Pest syntax
    });

    test('exception is abstract', function () {
        $reflection = new \ReflectionClass(ValidateLinksException::class);

        expect($reflection->isAbstract())->toBeTrue();
    });

    test('exception extends standard exception', function () {
        expect(ValidateLinksException::class)->toBeSubclassOf(\Exception::class);
    });

    test('exception can be extended', function () {
        $concreteException = new ConcreteValidateLinksException('Test message');

                expect($concreteException)->toBeInstanceOf(ValidateLinksException::class);
                expect($concreteException)->toBeInstanceOf(\Exception::class);
    });

    test('concrete exception has required methods', function () {
        $exception = new ConcreteValidateLinksException('Test message');

                // Test that exception implements required methods
                expect(method_exists($exception, 'getErrorCode'))->toBeTrue();
                expect(method_exists($exception, 'getSeverity'))->toBeTrue();
                expect(method_exists($exception, 'getContext'))->toBeTrue();
                expect(method_exists($exception, 'toArray'))->toBeTrue();
    });

    test('concrete exception has correct structure', function () {
        $exception = new ConcreteValidateLinksException('Test message', 100);

                // Test exception structure
                expect($exception->getErrorCode())->toBeString();
                expect($exception->getSeverity())->toBeString();
                expect($exception->getContext())->toBeArray();
                expect($exception->toArray())->toBeArray();
    });

    test('exception to array returns correct structure', function () {
        $exception = new ConcreteValidateLinksException('Test message', 100);
                $array = $exception->toArray();

                // Assert exception array structure
                $requiredKeys = ['error_code', 'message', 'severity', 'file', 'line', 'context'];
                foreach ($requiredKeys as $key) {
                    expect($array)->toHaveKey($key);
                }
    });

    test('exception error code is string', function () {
        $exception = new ConcreteValidateLinksException('Test message');

                $errorCode = $exception->getErrorCode();
                expect($errorCode)->toBeString();
                expect($errorCode)->not->toBeEmpty();
    });

    test('exception severity is string', function () {
        $exception = new ConcreteValidateLinksException('Test message');

                $severity = $exception->getSeverity();
                expect($severity)->toBeString();
                expect($severity)->not->toBeEmpty();
    });

    test('exception context is array', function () {
        $exception = new ConcreteValidateLinksException('Test message');

                $context = $exception->getContext();
                expect($context)->toBeArray();
    });

    test('exception can be created with context', function () {
        $context = ['key' => 'value', 'data' => ['nested' => 'value']];
                $exception = new ConcreteValidateLinksException('Test message', 0, null, $context);

                expect($exception->getContext())->toBe($context);
    });

    test('exception can be created with previous exception', function () {
        $previous = new \Exception('Previous exception');
                $exception = new ConcreteValidateLinksException('Test message', 0, $previous);

                expect($exception->getPrevious()->toBe($previous));
    });

    test('exception is serializable', function () {
        $exception = new ConcreteValidateLinksException('Test message', 100);

                // Test serialization
                $serialized = serialize($exception);
                expect($serialized)->toBeString();
                $unserialized = unserialize($serialized);
                expect($unserialized)->toBeInstanceOf(ValidateLinksException::class);
                expect($unserialized->getMessage())->toBe($exception->getMessage());
    });

    test('exception is json serializable', function () {
        $exception = new ConcreteValidateLinksException('Test message', 100);

                // Test JSON serialization
                $json = json_encode($exception->toArray());
                expect($json)->toBeString();
                $decoded = json_decode($json, true);
                expect($decoded)->toBeArray();
                expect($decoded['error_code'])->toBe($exception->getErrorCode());
    });

    test('exception to array includes all required fields', function () {
        $context = ['test_key' => 'test_value'];
                $exception = new ConcreteValidateLinksException('Test message', 100, null, $context);
                $array = $exception->toArray();

                expect($array)->toHaveKey('error_code');
                expect($array)->toHaveKey('message');
                expect($array)->toHaveKey('severity');
                expect($array)->toHaveKey('file');
                expect($array)->toHaveKey('line');
                expect($array)->toHaveKey('context');

                expect($array['error_code'])->toBe('TEST_ERROR');
                expect($array['message'])->toBe('Test message');
                expect($array['severity'])->toBe('medium');
                expect($array['context'])->toBe($context);
    });

    test('exception error code format is valid', function () {
        $exception = new ConcreteValidateLinksException('Test message');

                expect($exception->getErrorCode())->toMatch('/^[A-Z_]+$/');
    });

    test('exception handles empty context', function () {
        $exception = new ConcreteValidateLinksException('Test message');

                $context = $exception->getContext();
                expect($context)->toBeArray();
                expect($context)->toBeEmpty();
    });

    test('exception handles complex context', function () {
        $complexContext = [
                    'url' => 'https://example.com',
                    'status_code' => 404,
                    'headers' => ['Content-Type' => 'text/html'],
                    'metadata' => [
                        'timestamp' => time(),
                        'user_agent' => 'test-agent',
                        'nested' => [
                            'deep' => 'value'
                        ]
                    ]
                ];

                $exception = new ConcreteValidateLinksException('Test message', 0, null, $complexContext);

                expect($exception->getContext())->toBe($complexContext);
    });

    test('exception preserves stack trace', function () {
        $exception = new ConcreteValidateLinksException('Test message');

                $trace = $exception->getTrace();
                expect($trace)->toBeArray();
                expect($trace)->not->toBeEmpty();
    });

    test('exception has correct file and line', function () {
        $line = __LINE__ + 1;
                $exception = new ConcreteValidateLinksException('Test message');

                expect($exception->getFile()->toBe(__FILE__));
                expect($exception->getLine()->toBe($line));
    });

    test('exception can be caught as base exception', function () {
        // Test that exception is thrown with correct message
        expect(fn() => new ValidateLinksException('Test message'))
            ->toThrow(ValidateLinksException::class, 'Test message');

                throw new ConcreteValidateLinksException('Test message');
    });

    test('exception can be caught as validate links exception', function () {
        // Test that exception is thrown with correct message
        expect(fn() => new ValidateLinksException('Test message'))
            ->toThrow(ValidateLinksException::class, 'Test message');

                throw new ConcreteValidateLinksException('Test message');
    });

    test('exception supports different severity levels', function () {
        $lowSeverityException = new LowSeverityException('Low severity');
                $highSeverityException = new HighSeverityException('High severity');

                expect($lowSeverityException->getSeverity()->toBe('low'));
                expect($highSeverityException->getSeverity()->toBe('critical'));
    });

    test('exception supports different error codes', function () {
        $exception1 = new ConcreteValidateLinksException('Message 1');
                $exception2 = new AlternativeException('Message 2');

                expect($exception1->getErrorCode()->toBe('TEST_ERROR'));
                expect($exception2->getErrorCode()->toBe('ALTERNATIVE_ERROR'));
    });

    test('exception string representation includes error code', function () {
        $exception = new ConcreteValidateLinksException('Test message');
                $string = (string) $exception;

                expect($string)->toContain('TEST_ERROR');
                expect($string)->toContain('Test message');
    });

});
