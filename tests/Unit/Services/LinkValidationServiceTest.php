<?php

declare(strict_types=1);

use App\Services\LinkValidationService;

describe('LinkValidationService', function () {
    beforeEach(function () {
        $this->service = app(LinkValidationService::class);
    });

    describe('internal link validation', function () {
        test('validates existing internal links', function () {
            // Create test files
            if (!is_dir('docs')) mkdir('docs', 0755, true);
            file_put_contents('docs/test.md', '# Test Document');
            file_put_contents('docs/guide.md', "[Test Link](test.md)\n[Another Link](test.md#section)");

            $config = ['scopes' => ['internal']];
            $result = $this->service->validateFile('docs/guide.md', $config);

            expect($result)->toBeArray();
            expect($result)->toHaveKey('valid');
            expect($result['valid'])->toBeTrue();
        });

        test('detects broken internal links', function () {
            file_put_contents('docs/guide.md', '[Broken Link](missing.md)');

            $config = ['scopes' => ['internal']];
            $result = $this->service->validateFile('docs/guide.md', $config);

            expect($result)->toBeArray();
            expect($result)->toHaveKey('valid');
            expect($result['valid'])->toBeFalse();
            expect($result)->toHaveKey('broken_count');
            expect($result['broken_count'])->toBe(1);
        });

        afterEach(function () {
            // Clean up test files
            if (file_exists('docs/test.md')) unlink('docs/test.md');
            if (file_exists('docs/guide.md')) unlink('docs/guide.md');
            if (is_dir('docs') && count(scandir('docs')) == 2) rmdir('docs');
        });
    });

    describe('external link validation', function () {
        test('validates working external links', function () {
            $urls = ['https://example.com', 'https://github.com'];
            // Mock successful HTTP responses would go here in a real implementation

            $config = ['scopes' => ['external']];
            $result = $this->service->validateExternalLinks($urls, $config);

            expect($result)->toBeArray();
            expect($result)->toHaveKey('valid');
            expect($result['valid'])->toBeTrue();
        });

        test('handles various HTTP error scenarios', function () {
            // Mock mixed HTTP responses would go here in a real implementation
            $urls = [
                'https://not-found.example',
                'https://server-error.example',
                'https://forbidden.example'
            ];

            $config = ['scopes' => ['external']];
            $result = $this->service->validateExternalLinks($urls, $config);

            expect($result)->toBeArray();
            expect($result)->toHaveKey('valid');
            expect($result['valid'])->toBeFalse();
            expect($result)->toHaveKey('broken_count');
            expect($result['broken_count'])->toBe(3);
        });
    });
});
