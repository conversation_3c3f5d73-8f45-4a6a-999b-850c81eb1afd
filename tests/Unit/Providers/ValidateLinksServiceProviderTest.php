<?php

declare(strict_types=1);

use App\Providers\ValidateLinksServiceProvider;
use App\Services\Contracts\GitHubAnchorInterface;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\ReportingInterface;
use App\Services\Contracts\SecurityValidationInterface;
use App\Services\Contracts\StatisticsInterface;
use App\Services\LinkValidationService;
use Illuminate\Support\ServiceProvider;
use Tests\Support\Traits\AssertionHelpers;

uses(AssertionHelpers::class);

describe('ValidateLinksServiceProvider', function () {
    beforeEach(function () {
        $this->provider = new ValidateLinksServiceProvider(app());
    });

    test('provider extends service provider', function () {
        expect($this->provider)->toBeInstanceOf(ServiceProvider::class);
    });

    test('provider can be instantiated', function () {
        $provider = new ValidateLinksServiceProvider(app());

                expect($provider)->toBeInstanceOf(ValidateLinksServiceProvider::class);
    });

    test('provider has register method', function () {
        expect(method_exists($this->provider, 'register')->toBeTrue());

                $reflection = new \ReflectionMethod($this->provider, 'register');
                expect($reflection->isPublic()->toBeTrue());
                expect($reflection->getReturnType()->toBeNull());
    });

    test('provider has boot method', function () {
        expect(method_exists($this->provider, 'boot')->toBeTrue());

                $reflection = new \ReflectionMethod($this->provider, 'boot');
                expect($reflection->isPublic()->toBeTrue());
                expect($reflection->getReturnType()->toBeNull());
    });

    test('register method executes without errors', function () {
        // No assertions needed;

                $this->provider->register();
    });

    test('boot method executes without errors', function () {
        // No assertions needed;

                $this->provider->boot();
    });

    test('services are registered', function () {
        $this->provider->register();

                expect(app()->toBeTrue()->bound(LinkValidationInterface::class));
                expect(app()->toBeInstanceOf(
                    LinkValidationService::class)->make(LinkValidationInterface::class)
                );
    });

    test('singleton services return same instance', function () {
        $this->provider->register();

                $instance1 = app()->make(LinkValidationInterface::class);
                $instance2 = app()->make(LinkValidationInterface::class);

                expect($instance2)->toBe($instance1);
    });

    test('provider registers link validation interface', function () {
        $this->provider->register();

                expect(app()->toBeTrue()->bound(LinkValidationInterface::class));

                $service = app()->make(LinkValidationInterface::class);
                expect($service)->toBeInstanceOf(LinkValidationInterface::class);
    });

    test('provider registers reporting interface', function () {
        $this->provider->register();

                expect(app()->toBeTrue()->bound(ReportingInterface::class));

                $service = app()->make(ReportingInterface::class);
                expect($service)->toBeInstanceOf(ReportingInterface::class);
    });

    test('provider registers github anchor interface', function () {
        $this->provider->register();

                expect(app()->toBeTrue()->bound(GitHubAnchorInterface::class));

                $service = app()->make(GitHubAnchorInterface::class);
                expect($service)->toBeInstanceOf(GitHubAnchorInterface::class);
    });

    test('provider registers security validation interface', function () {
        $this->provider->register();

                expect(app()->toBeTrue()->bound(SecurityValidationInterface::class));

                $service = app()->make(SecurityValidationInterface::class);
                expect($service)->toBeInstanceOf(SecurityValidationInterface::class);
    });

    test('provider registers statistics interface', function () {
        $this->provider->register();

                expect(app()->toBeTrue()->bound(StatisticsInterface::class));

                $service = app()->make(StatisticsInterface::class);
                expect($service)->toBeInstanceOf(StatisticsInterface::class);
    });

    test('provider registers services as singletons', function () {
        $this->provider->register();

                // Test that services are registered as singletons
                $service1 = app()->make(LinkValidationInterface::class);
                $service2 = app()->make(LinkValidationInterface::class);

                expect($service2)->toBe($service1);
    });

    test('provider publishes configuration', function () {
        $this->provider->register();
                $this->provider->boot();

                // Verify that configuration is published
                // In a real implementation, this would check for published config files
                expect(true)->toBeTrue();
    });

    test('provider loads configuration from file', function () {
        $this->provider->register();
                $this->provider->boot();

                // Test that validate-links configuration is loaded
                $config = app('config');

                // Check if validate-links config exists
                $validateLinksConfig = $config->get('validate-links');
                expect($validateLinksConfig)->toBeArray();
    });

    test('provider has correct namespace', function () {
        $reflection = new \ReflectionClass(ValidateLinksServiceProvider::class);

                expect($reflection->getNamespaceName()->toBe('App\Providers'));
    });

    test('provider file uses strict types', function () {
        $reflection = new \ReflectionClass(ValidateLinksServiceProvider::class);
                $filename = $reflection->getFileName();
                $content = file_get_contents($filename);

                expect($content)->toContain('declare(strict_types=1);');
    });

});
