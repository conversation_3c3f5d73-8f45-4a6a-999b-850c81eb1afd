<?php

declare(strict_types=1);

use App\Commands\ReportCommand;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\ReportingInterface;
use App\Services\ValueObjects\ValidationConfig;
use Tests\Support\Traits\AssertionHelpers;
use Tests\Support\Traits\FileTestHelpers;
use Tests\Support\Traits\MockingHelpers;

uses(AssertionHelpers::class, FileTestHelpers::class, MockingHelpers::class);

describe('ReportCommand', function () {
    beforeEach(function () {
        $this->mockLinkValidation = $this->mockLinkValidation();
        $this->mockReporting = $this->mockReporting();

        $this->command = new ReportCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        );
    });

    afterEach(function () {
        $this->cleanupTestFiles();
    });

    test('it can be instantiated with required dependencies', function () {
        $command = new ReportCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        );

        expect($command)->toBeInstanceOf(ReportCommand::class);
    });

    test('handle returns failure when no paths provided', function () {
        $this->artisan('report')
            ->expectsOutput('No paths provided')
            ->assertExitCode(1);
    });

    test('handle processes single path successfully', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => [
                'return' => ['summary' => ['broken_links' => 0]],
                'times' => 1,
            ],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => [
                'return' => 0,
                'times' => 1,
            ],
        ]);

        $this->artisan('report', ['paths' => [$testFile]])
            ->expectsOutput('📊 Generating link validation report')
            ->assertExitCode(0);
    });

    test('handle processes multiple paths', function () {
        $testFile1 = $this->createTestFile('test1.md', '# Test file 1');
        $testFile2 = $this->createTestFile('test2.md', '# Test file 2');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => [
                'return' => ['summary' => ['broken_links' => 0]],
                'times' => 1,
            ],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => [
                'return' => 0,
                'times' => 1,
            ],
        ]);

        $this->artisan('report', ['paths' => [$testFile1, $testFile2]])
            ->expectsOutput('📊 Generating link validation report')
            ->assertExitCode(0);
    });

    test('handle uses default html format', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $capturedConfig = null;
        $this->mockLinkValidation
            ->shouldReceive('validateFiles')
            ->once()
            ->with(Mockery::any(), Mockery::capture($capturedConfig))
            ->andReturn(['summary' => ['broken_links' => 0]]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', ['paths' => [$testFile]])
            ->assertExitCode(0);

        // In a real implementation, we would verify the captured config format
        expect($capturedConfig)->not->toBeNull();
    });

    test('handle accepts custom format option', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => ['summary' => ['broken_links' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', [
            'paths' => [$testFile],
            '--format' => 'json'
        ])->assertExitCode(0);
    });

    test('handle accepts output file option', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $outputFile = $this->getTestPath('report.html');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => ['summary' => ['broken_links' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', [
            'paths' => [$testFile],
            '--output' => $outputFile
        ])->assertExitCode(0);
    });

    test('handle accepts detailed option', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => ['summary' => ['broken_links' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', [
            'paths' => [$testFile],
            '--detailed' => true
        ])->assertExitCode(0);
    });

    test('handle creates validation config with correct options', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $outputFile = $this->getTestPath('report.json');

        $capturedConfig = null;
        $this->mockLinkValidation
            ->shouldReceive('validateFiles')
            ->once()
            ->with(Mockery::any(), Mockery::capture($capturedConfig))
            ->andReturn(['summary' => ['broken_links' => 0]]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', [
            'paths' => [$testFile],
            '--format' => 'json',
            '--output' => $outputFile
        ])->assertExitCode(0);

        expect($capturedConfig)->toBeInstanceOf(ValidationConfig::class);
    });

    test('handle calls validator with collected files', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->mockLinkValidation
            ->shouldReceive('validateFiles')
            ->once()
            ->with(
                Mockery::on(function ($files) use ($testFile) {
                    return is_array($files) && in_array($testFile, $files);
                }),
                Mockery::type(ValidationConfig::class)
            )
            ->andReturn(['summary' => ['broken_links' => 0]]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', ['paths' => [$testFile]])
            ->assertExitCode(0);
    });

    test('handle calls reporter with validation results', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $validationResults = ['summary' => ['broken_links' => 0]];

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => $validationResults],
        ]);

        $this->mockReporting
            ->shouldReceive('generateReport')
            ->once()
            ->with(
                $validationResults,
                Mockery::type(ValidationConfig::class),
                Mockery::type(ReportCommand::class)
            )
            ->andReturn(0);

        $this->artisan('report', ['paths' => [$testFile]])
            ->assertExitCode(0);
    });

    test('handle returns reporter exit code', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => ['summary' => ['broken_links' => 1]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 1],
        ]);

        $this->artisan('report', ['paths' => [$testFile]])
            ->assertExitCode(1);
    });

    test('handle processes directory paths', function () {
        $testDir = $this->getTestPath('docs');
        mkdir($testDir, 0755, true);
        $this->createTestFile('docs/file1.md', '# File 1');
        $this->createTestFile('docs/file2.md', '# File 2');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => ['summary' => ['broken_links' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', ['paths' => [$testDir]])
            ->assertExitCode(0);
    });

    test('command signature includes all expected options', function () {
        $reflection = new \ReflectionClass(ReportCommand::class);
        $signatureProperty = $reflection->getProperty('signature');
        $signatureProperty->setAccessible(true);

        $signature = $signatureProperty->getValue(new ReportCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        ));

        expect($signature)->toContain('report');
        expect($signature)->toContain('paths*');
        expect($signature)->toContain('--format=html');
        expect($signature)->toContain('--output=');
        expect($signature)->toContain('--detailed');
    });

    test('command description is set', function () {
        $reflection = new \ReflectionClass(ReportCommand::class);
        $descriptionProperty = $reflection->getProperty('description');
        $descriptionProperty->setAccessible(true);

        $description = $descriptionProperty->getValue(new ReportCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        ));

        expect($description)->toBeString();
        expect($description)->not->toBeEmpty();
        expect(strtolower($description))->toContain('report');
        expect(strtolower($description))->toContain('validation');
    });

    test('handle shows progress message', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => ['summary' => ['broken_links' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', ['paths' => [$testFile]])
            ->expectsOutput('📊 Generating link validation report')
            ->assertExitCode(0);
    });

    test('handle with all options creates comprehensive config', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $outputFile = $this->getTestPath('detailed-report.json');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFiles' => ['return' => ['summary' => ['broken_links' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 0],
        ]);

        $this->artisan('report', [
            'paths' => [$testFile],
            '--format' => 'json',
            '--output' => $outputFile,
            '--detailed' => true
        ])->assertExitCode(0);
    });
});
