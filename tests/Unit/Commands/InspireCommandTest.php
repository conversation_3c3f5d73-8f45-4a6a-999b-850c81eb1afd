<?php

declare(strict_types=1);

use App\Commands\InspireCommand;
use Illuminate\Console\Scheduling\Schedule;
use Tests\Support\Traits\AssertionHelpers;
use Tests\Support\Traits\CommandTestHelpers;

uses(AssertionHelpers::class, CommandTestHelpers::class);

describe('InspireCommand', function () {
    beforeEach(function () {
        $this->setUpCommandTest();
        $this->command = new InspireCommand();
    });

    afterEach(function () {
        $this->tearDownCommandTest();
    });

    test('it can be instantiated', function () {
        $command = new InspireCommand();

        expect($command)->toBeInstanceOf(InspireCommand::class);
    });

    test('handle executes successfully', function () {
        $this->artisan('inspire')
            ->assertExitCode(0);
    });

    test('handle accepts name argument', function () {
        $this->artisan('inspire', ['name' => 'TestUser'])
            ->assertExitCode(0);
    });

    test('handle uses default name when not provided', function () {
        $this->artisan('inspire')
            ->assertExitCode(0);
    });

    test('handle method returns void', function () {
        $result = $this->command->handle();

        expect($result)->toBeNull();
    });

    test('command signature is correctly defined', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);
        $signatureProperty = $reflection->getProperty('signature');
        $signatureProperty->setAccessible(true);

        $signature = $signatureProperty->getValue(new InspireCommand());

        expect($signature)->toContain('inspire');
        expect($signature)->toContain('name=Artisan');
    });

    test('command description is set', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);
        $descriptionProperty = $reflection->getProperty('description');
        $descriptionProperty->setAccessible(true);

        $description = $descriptionProperty->getValue(new InspireCommand());

        expect($description)->toBeString();
        expect($description)->not->toBeEmpty();
        expect($description)->toBe('Display an inspiring quote');
    });

    test('schedule method exists and accepts schedule parameter', function () {
        $schedule = $this->createMock(Schedule::class);

        $this->command->schedule($schedule);
    })->throwsNoExceptions();

    test('schedule method has correct signature', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);
        $scheduleMethod = $reflection->getMethod('schedule');

        expect($scheduleMethod->isPublic())->toBeTrue();

        $parameters = $scheduleMethod->getParameters();
        expect($parameters)->toHaveCount(1);

        $scheduleParam = $parameters[0];
        expect($scheduleParam->getName())->toBe('schedule');
        expect($scheduleParam->getType()?->getName())->toBe(Schedule::class);
    });

    test('schedule method returns void', function () {
        $schedule = $this->createMock(Schedule::class);
        $result = $this->command->schedule($schedule);

        expect($result)->toBeNull();
    });

    test('command renders termwind output', function () {
        // Since we can't easily test Termwind output in unit tests,
        // we verify that the handle method executes without errors
        ob_start();
        $this->command->handle();
        $output = ob_get_clean();

        // The command should execute without throwing exceptions
        expect(true)->toBeTrue();
    });

    test('command is final class', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);

        expect($reflection->isFinal())->toBeTrue();
    });

    test('command extends laravel zero command', function () {
        expect($this->command)->toBeInstanceOf(\LaravelZero\Framework\Commands\Command::class);
    });

    test('command has proper namespace', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);

        expect($reflection->getNamespaceName())->toBe('App\Commands');
    });

    test('command uses strict types', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);
        $filename = $reflection->getFileName();
        $content = file_get_contents($filename);

        expect($content)->toContain('declare(strict_types=1);');
    });

    test('command imports required classes', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);
        $filename = $reflection->getFileName();
        $content = file_get_contents($filename);

        expect($content)->toContain('use Illuminate\Console\Scheduling\Schedule;');
        expect($content)->toContain('use LaravelZero\Framework\Commands\Command;');
        expect($content)->toContain('use function Termwind\render;');
    });

    test('command has proper docblocks', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);

        // Check signature property docblock
        $signatureProperty = $reflection->getProperty('signature');
        $docComment = $signatureProperty->getDocComment();
        expect($docComment)->toContain('The signature of the command');

        // Check description property docblock
        $descriptionProperty = $reflection->getProperty('description');
        $docComment = $descriptionProperty->getDocComment();
        expect($docComment)->toContain('The description of the command');

        // Check handle method docblock
        $handleMethod = $reflection->getMethod('handle');
        $docComment = $handleMethod->getDocComment();
        expect($docComment)->toContain('Execute the console command');

        // Check schedule method docblock
        $scheduleMethod = $reflection->getMethod('schedule');
        $docComment = $scheduleMethod->getDocComment();
        expect($docComment)->toContain('Define the command\'s schedule');
    });

    test('command signature has correct default value', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);
        $signatureProperty = $reflection->getProperty('signature');
        $signatureProperty->setAccessible(true);

        $signature = $signatureProperty->getValue(new InspireCommand());

        expect($signature)->toContain('{name=Artisan}');
    });

    test('command can be called with different names', function () {
        $testNames = ['Developer', 'Tester', 'User', 'Admin'];

        foreach ($testNames as $name) {
            $this->artisan('inspire', ['name' => $name])
                ->assertExitCode(0);
        }
    });

    test('command handles empty name argument', function () {
        $this->artisan('inspire', ['name' => ''])
            ->assertExitCode(0);
    });

    test('command handles special characters in name', function () {
        $specialNames = ['Test-User', 'Test_User', 'Test User', 'Test@User'];

        foreach ($specialNames as $name) {
            $this->artisan('inspire', ['name' => $name])
                ->assertExitCode(0);
        }
    });

    test('schedule method contains commented example', function () {
        $reflection = new \ReflectionClass(InspireCommand::class);
        $filename = $reflection->getFileName();
        $content = file_get_contents($filename);

        // Check that the schedule method contains the commented example
        expect($content)->toContain('// $schedule->command(static::class)->everyMinute();');
    });
});
