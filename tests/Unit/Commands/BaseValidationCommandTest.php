<?php

declare(strict_types=1);

use App\Commands\BaseValidationCommand;
use App\Enums\LinkStatus;
use Tests\Support\Doubles\Commands\TestableBaseValidationCommand;
use Tests\Support\Traits\AssertionHelpers;
use Tests\Support\Traits\CommandTestHelpers;
use Tests\Support\Traits\MockingHelpers;

uses(AssertionHelpers::class, CommandTestHelpers::class, MockingHelpers::class);

describe('BaseValidationCommand', function () {
    beforeEach(function () {
        $this->setUpCommandTest();

        $this->mockLinkValidation = $this->mockLinkValidation();
        $this->mockReporting = $this->mockReporting();

        $this->command = new TestableBaseValidationCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        );
    });

    afterEach(function () {
        $this->tearDownCommandTest();
    });

    test('it can be instantiated with required dependencies', function () {
        $command = new TestableBaseValidationCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        );

        expect($command)->toBeInstanceOf(BaseValidationCommand::class);
        expect($command)->toBeInstanceOf(TestableBaseValidationCommand::class);
    });

    test('constructor sets dependencies correctly', function () {
        expect($this->command->getLinkValidation())->toBe($this->mockLinkValidation);
        expect($this->command->getReporting())->toBe($this->mockReporting);
    });

    test('collect files returns array for single file', function () {
        $testFile = $this->createTestFile('test.md', '# Test');
        $config = $this->createSampleValidationConfig();

        $files = $this->command->collectFiles([$testFile], $config);

        expect($files)->toBeArray();
        expect($files)->toContain($testFile);
        expect($files)->toHaveCount(1);
    });

    test('collect files returns array for directory', function () {
        $testDir = $this->getTestPath('docs');
        mkdir($testDir, 0755, true);

        $file1 = $this->createTestFile('docs/file1.md', '# File 1');
        $file2 = $this->createTestFile('docs/file2.md', '# File 2');

        $config = $this->createSampleValidationConfig();

        $files = $this->command->collectFiles([$testDir], $config);

        expect($files)->toBeArray();
        expect(count($files))->toBeGreaterThanOrEqual(2);
        expect($files)->toContain($file1);
        expect($files)->toContain($file2);
    });

    test('collect files handles mixed paths', function () {
        $testFile = $this->createTestFile('single.md', '# Single');
        $testDir = $this->getTestPath('mixed');
        mkdir($testDir, 0755, true);
        $dirFile = $this->createTestFile('mixed/dir.md', '# Dir');

        $config = $this->createSampleValidationConfig();

        $files = $this->command->collectFiles([$testFile, $testDir], $config);

        expect($files)->toBeArray();
        expect($files)->toContain($testFile);
        expect($files)->toContain($dirFile);
    });

    test('validate command options accepts valid scope', function () {
        $this->command->setOption('scope', 'internal');
        $this->command->setOption('format', 'console');

        $this->command->validateCommandOptions();
    })->throwsNoExceptions();

    test('validate command options accepts all scope', function () {
        $this->command->setOption('scope', 'all');
        $this->command->setOption('format', 'json');

        $this->command->validateCommandOptions();
    })->throwsNoExceptions();

    test('validate command options accepts comma separated scopes', function () {
        $this->command->setOption('scope', 'internal,external,anchor');
        $this->command->setOption('format', 'html');

        $this->command->validateCommandOptions();
    })->throwsNoExceptions();

    test('validate command options rejects invalid scope', function () {
        $this->command->setOption('scope', 'invalid_scope');
        $this->command->setOption('format', 'console');

        $this->command->validateCommandOptions();
    })->throws(\Exception::class);

    test('validate command options rejects invalid format', function () {
        $this->command->setOption('scope', 'all');
        $this->command->setOption('format', 'invalid_format');

        $this->command->validateCommandOptions();
    })->throws(\Exception::class);

    test('process validation results returns success for no broken links', function () {
        $results = [
            'summary' => ['broken_links' => 0],
            'broken_links' => [],
        ];

        $exitCode = $this->command->processValidationResults($results);

        expect($exitCode)->toBe(0);
    });

    test('process validation results returns failure for broken links', function () {
        $results = [
            'summary' => ['broken_links' => 2],
            'broken_links' => [
                ['url' => 'https://broken1.com', 'status' => LinkStatus::BROKEN->value],
                ['url' => 'https://broken2.com', 'status' => LinkStatus::NOT_FOUND->value],
            ],
        ];

        $exitCode = $this->command->processValidationResults($results);

        expect($exitCode)->toBe(1);
    });

    test('execute validation calls link validation service', function () {
        $files = ['test.md'];
        $config = $this->createSampleValidationConfig();
        $expectedResults = ['summary' => ['broken_links' => 0]];

        $this->mockLinkValidation
            ->shouldReceive('validateFiles')
            ->once()
            ->with($files, $config)
            ->andReturn($expectedResults);

        $result = $this->command->executeValidation($files, $config);

        expect($result)->toBe($expectedResults);
    });

    test('execute validation handles exceptions', function () {
        $files = ['test.md'];
        $config = $this->createSampleValidationConfig();

        $this->mockLinkValidation
            ->shouldReceive('validateFiles')
            ->once()
            ->andThrow(new \Exception('Validation failed'));

        $this->command->executeValidation($files, $config);
    })->throws(\Exception::class, 'Validation failed');

    test('handle validation error returns error exit code', function () {
        $exception = new \Exception('Test error');

        $exitCode = $this->command->handleValidationError($exception);

        expect($exitCode)->toBe(2);
    });

    test('handle validation error shows verbose output when enabled', function () {
        $this->command->setOption('verbose', true);
        $exception = new \Exception('Test error');

        $this->command->handleValidationError($exception);

        // In a real implementation, we would capture and assert output
        expect(true)->toBeTrue(); // Placeholder assertion
    });

    test('group links by status organizes results correctly', function () {
        $brokenLinks = [
            ['url' => 'https://valid.com', 'status' => LinkStatus::VALID->value],
            ['url' => 'https://broken.com', 'status' => LinkStatus::BROKEN->value],
            ['url' => 'https://notfound.com', 'status' => LinkStatus::NOT_FOUND->value],
            ['url' => 'https://timeout.com', 'status' => LinkStatus::TIMEOUT->value],
        ];

        $groups = $this->command->groupLinksByStatus($brokenLinks);

        expect($groups)->toBeArray();
        expect($groups)->toHaveKey(LinkStatus::VALID->value);
        expect($groups)->toHaveKey(LinkStatus::BROKEN->value);
        expect($groups)->toHaveKey(LinkStatus::NOT_FOUND->value);
        expect($groups)->toHaveKey(LinkStatus::TIMEOUT->value);
    });

    test('display status groups handles empty groups', function () {
        $statusGroups = [];

        $this->command->displayStatusGroups($statusGroups);
    })->throwsNoExceptions();

    test('display validation summary shows summary information', function () {
        $summary = [
            'total_links' => 10,
            'valid_links' => 8,
            'broken_links' => 2,
            'execution_time' => 1.5,
        ];

        $this->command->displayValidationSummary($summary);
    })->throwsNoExceptions();
});


