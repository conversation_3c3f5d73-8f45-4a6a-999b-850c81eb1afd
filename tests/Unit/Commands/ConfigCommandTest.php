<?php

declare(strict_types=1);

use App\Commands\ConfigCommand;
use Tests\Support\Traits\AssertionHelpers;
use Tests\Traits\FileTestHelpers;

uses(AssertionHelpers::class, FileTestHelpers::class);

describe('ConfigCommand', function () {
    beforeEach(function () {
        $this->command = new ConfigCommand();
        $this->testConfigPath = $this->getTestPath('validate-links.json');
    });

    afterEach(function () {
        // Clean up test config file
        if (file_exists($this->testConfigPath)) {
            unlink($this->testConfigPath);
        }
    });

    test('it can be instantiated', function () {
        $command = new ConfigCommand();

        expect($command)->toBeInstanceOf(ConfigCommand::class);
    });

    test('handle shows usage when no options provided', function () {
        $this->artisan('config')
            ->expectsOutput('Use --init to create a configuration file or --show to display current settings')
            ->assertExitCode(0);
    });

    test('handle with init option creates configuration file', function () {
        // Mock the working directory to our test path
        $originalCwd = getcwd();
        chdir(dirname($this->testConfigPath));

        try {
            $this->artisan('config', ['--init' => true])
                ->expectsQuestion('Default validation scope', ['internal', 'anchor'])
                ->expectsQuestion('External link timeout (seconds)', '30')
                ->expectsQuestion('Maximum broken links before stopping', '50')
                ->expectsOutput('✅ Configuration saved to ' . basename($this->testConfigPath))
                ->assertExitCode(0);

            expect(basename($this->testConfigPath))->toBeFile();

            $config = json_decode(file_get_contents(basename($this->testConfigPath)), true);
            expect($config)->toBeArray();
            expect($config)->toHaveKey('scope');
            expect($config)->toHaveKey('timeout');
            expect($config)->toHaveKey('max_broken');
        } finally {
            chdir($originalCwd);
            if (file_exists(basename($this->testConfigPath))) {
                unlink(basename($this->testConfigPath));
            }
        }
    });

    test('handle with init option prompts for overwrite when file exists', function () {
        $originalCwd = getcwd();
        chdir(dirname($this->testConfigPath));

        try {
            // Create existing config file
            file_put_contents(basename($this->testConfigPath), '{"existing": true}');

            $this->artisan('config', ['--init' => true])
                ->expectsConfirmation(
                    'Configuration file already exists at ' . basename($this->testConfigPath) . '. Overwrite?',
                    'no'
                )
                ->expectsOutput('Configuration initialization cancelled')
                ->assertExitCode(0);

            // File should still contain original content
            $config = json_decode(file_get_contents(basename($this->testConfigPath)), true);
            expect($config)->toHaveKey('existing');
            expect($config['existing'])->toBeTrue();
        } finally {
            chdir($originalCwd);
            if (file_exists(basename($this->testConfigPath))) {
                unlink(basename($this->testConfigPath));
            }
        }
    });

    test('handle with init option overwrites when confirmed', function () {
        $originalCwd = getcwd();
        chdir(dirname($this->testConfigPath));

        try {
            // Create existing config file
            file_put_contents(basename($this->testConfigPath), '{"existing": true}');

            $this->artisan('config', ['--init' => true])
                ->expectsConfirmation(
                    'Configuration file already exists at ' . basename($this->testConfigPath) . '. Overwrite?',
                    'yes'
                )
                ->expectsQuestion('Default validation scope', ['internal', 'anchor'])
                ->expectsQuestion('External link timeout (seconds)', '30')
                ->expectsQuestion('Maximum broken links before stopping', '50')
                ->expectsOutput('✅ Configuration saved to ' . basename($this->testConfigPath))
                ->assertExitCode(0);

            // File should contain new configuration
            $config = json_decode(file_get_contents(basename($this->testConfigPath)), true);
            expect($config)->not->toHaveKey('existing');
            expect($config)->toHaveKey('scope');
        } finally {
            chdir($originalCwd);
            if (file_exists(basename($this->testConfigPath))) {
                unlink(basename($this->testConfigPath));
            }
        }
    });

    test('handle with show option displays current configuration', function () {
        $this->artisan('config', ['--show' => true])
            ->expectsOutput('📋 Current Configuration:')
            ->assertExitCode(0);
    });

    test('initialize config creates valid json structure', function () {
        $originalCwd = getcwd();
        chdir(dirname($this->testConfigPath));

        try {
            $this->artisan('config', ['--init' => true])
                ->expectsQuestion('Default validation scope', ['internal', 'external'])
                ->expectsQuestion('External link timeout (seconds)', '45')
                ->expectsQuestion('Maximum broken links before stopping', '25')
                ->assertExitCode(0);

            expect(basename($this->testConfigPath))->toBeFile();

            $config = json_decode(file_get_contents(basename($this->testConfigPath)), true);

            // Verify structure
            expect($config)->toBeArray();
            expect($config)->toHaveKey('scope');
            expect($config)->toHaveKey('timeout');
            expect($config)->toHaveKey('max_broken');
            expect($config)->toHaveKey('check_external');
            expect($config)->toHaveKey('format');
            expect($config)->toHaveKey('include_hidden');
            expect($config)->toHaveKey('case_sensitive');

            // Verify values
            expect($config['scope'])->toBe(['internal', 'external']);
            expect($config['timeout'])->toBe(45);
            expect($config['max_broken'])->toBe(25);
            expect($config['check_external'])->toBeTrue();
            expect($config['format'])->toBe('console');
            expect($config['include_hidden'])->toBeFalse();
            expect($config['case_sensitive'])->toBeFalse();
        } finally {
            chdir($originalCwd);
            if (file_exists(basename($this->testConfigPath))) {
                unlink(basename($this->testConfigPath));
            }
        }
    });

    test('initialize config sets check external based on scope', function () {
        $originalCwd = getcwd();
        chdir(dirname($this->testConfigPath));

        try {
            // Test with external scope
            $this->artisan('config', ['--init' => true])
                ->expectsQuestion('Default validation scope', ['external'])
                ->expectsQuestion('External link timeout (seconds)', '30')
                ->expectsQuestion('Maximum broken links before stopping', '50')
                ->assertExitCode(0);

            $config = json_decode(file_get_contents(basename($this->testConfigPath)), true);
            expect($config['check_external'])->toBeTrue();

            unlink(basename($this->testConfigPath));

            // Test without external scope
            $this->artisan('config', ['--init' => true])
                ->expectsQuestion('Default validation scope', ['internal'])
                ->expectsQuestion('External link timeout (seconds)', '30')
                ->expectsQuestion('Maximum broken links before stopping', '50')
                ->assertExitCode(0);

            $config = json_decode(file_get_contents(basename($this->testConfigPath)), true);
            expect($config['check_external'])->toBeFalse();
        } finally {
            chdir($originalCwd);
            if (file_exists(basename($this->testConfigPath))) {
                unlink(basename($this->testConfigPath));
            }
        }
    });

    test('initialize config handles file write failure', function () {
        // Create a directory where the config file should be (to cause write failure)
        $originalCwd = getcwd();
        $testDir = $this->getTestPath('readonly');
        mkdir($testDir, 0444, true); // Read-only directory
        chdir($testDir);

        try {
            $this->artisan('config', ['--init' => true])
                ->expectsQuestion('Default validation scope', ['internal'])
                ->expectsQuestion('External link timeout (seconds)', '30')
                ->expectsQuestion('Maximum broken links before stopping', '50')
                ->expectsOutput('❌ Failed to save configuration to ' . getcwd() . '/validate-links.json')
                ->assertExitCode(1);
        } finally {
            chdir($originalCwd);
            chmod($testDir, 0755); // Restore permissions for cleanup
            if (is_dir($testDir)) {
                rmdir($testDir);
            }
        }
    });

    test('show config displays formatted json', function () {
        // Set some test configuration
        config(['validate-links.test_key' => 'test_value']);
        config(['validate-links.nested.key' => 'nested_value']);

        $this->artisan('config', ['--show' => true])
            ->expectsOutput('📋 Current Configuration:')
            ->assertExitCode(0);
    });

    test('gather configuration settings returns valid structure', function () {
        // This would require mocking the prompt functions, which is complex
        // In a real implementation, we would extract the logic to a separate method
        // and test it independently
        expect(true)->toBeTrue(); // Placeholder for complex prompt testing
    });

    test('command signature is correctly defined', function () {
        $reflection = new \ReflectionClass(ConfigCommand::class);
        $signatureProperty = $reflection->getProperty('signature');
        $signatureProperty->setAccessible(true);

        $signature = $signatureProperty->getValue(new ConfigCommand());

        expect($signature)->toContain('config');
        expect($signature)->toContain('--init');
        expect($signature)->toContain('--show');
    });

    test('command description is set', function () {
        $reflection = new \ReflectionClass(ConfigCommand::class);
        $descriptionProperty = $reflection->getProperty('description');
        $descriptionProperty->setAccessible(true);

        $description = $descriptionProperty->getValue(new ConfigCommand());

        expect($description)->toBeString();
        expect($description)->not->toBeEmpty();
        expect(strtolower($description))->toContain('configuration');
    });
});
