<?php

declare(strict_types=1);

use App\Commands\ValidateCommand;
use App\Enums\OutputFormat;
use App\Enums\ValidationScope;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\ReportingInterface;
use App\Services\ValueObjects\ValidationConfig;
use Tests\Support\Traits\AssertionHelpers;
use Tests\Support\Traits\FileTestHelpers;
use Tests\Support\Traits\MockingHelpers;

uses(AssertionHelpers::class, FileTestHelpers::class, MockingHelpers::class);

describe('ValidateCommand', function () {
    beforeEach(function () {
        $this->mockLinkValidation = $this->mockLinkValidation();
        $this->mockReporting = $this->mockReporting();

        $this->command = new ValidateCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        );
    });

    afterEach(function () {
        $this->cleanupTestFiles();
    });

    test('it can be instantiated with required dependencies', function () {
        $command = new ValidateCommand(
            $this->mockLinkValidation,
            $this->mockReporting
        );

        expect($command)->toBeInstanceOf(ValidateCommand::class);
    });

    test('handle validates default docs path when no path provided', function () {
        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 0, 'valid' => 0, 'invalid' => 0]],
        ]);

        $this->artisan('validate')
            ->expectsOutput('🔍 Validating links in: ./docs')
            ->assertExitCode(0);
    });

    test('handle validates specified path', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 1, 'valid' => 1, 'invalid' => 0]],
        ]);

        $this->artisan('validate', ['path' => $testFile])
            ->expectsOutput("🔍 Validating links in: {$testFile}")
            ->assertExitCode(0);
    });

    test('handle uses default scope all', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 1, 'valid' => 1, 'invalid' => 0]],
        ]);

        $this->artisan('validate', ['path' => $testFile])
            ->expectsOutput('📋 Scope: all')
            ->assertExitCode(0);
    });

    test('handle accepts custom scope option', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 1, 'valid' => 1, 'invalid' => 0]],
        ]);

        $this->artisan('validate', [
            'path' => $testFile,
            '--scope' => 'internal'
        ])
            ->expectsOutput('📋 Scope: internal')
            ->assertExitCode(0);
    });

    test('handle uses default console format', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 1, 'valid' => 1, 'invalid' => 0]],
        ]);

        $this->artisan('validate', ['path' => $testFile])
            ->expectsOutput('📄 Format: console')
            ->assertExitCode(0);
    });

    test('handle accepts custom format option', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 1, 'valid' => 1, 'invalid' => 0]],
        ]);

        $this->artisan('validate', [
            'path' => $testFile,
            '--format' => 'json'
        ])
            ->expectsOutput('📄 Format: json')
            ->assertExitCode(0);
    });

    test('handle shows output file when specified', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $outputFile = $this->getTestPath('output.json');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'exportReport' => ['return' => true],
            'generateSummary' => ['return' => ['total' => 1, 'valid' => 1, 'invalid' => 0]],
        ]);

        $this->artisan('validate', [
            'path' => $testFile,
            '--output' => $outputFile
        ])
            ->expectsOutput("💾 Output: {$outputFile}")
            ->expectsOutput("✅ Report saved to: {$outputFile}")
            ->assertExitCode(0);
    });

    test('handle creates validation config with options', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $capturedConfig = null;
        $this->mockLinkValidation
            ->shouldReceive('validateFile')
            ->once()
            ->with($testFile, Mockery::capture($capturedConfig))
            ->andReturn(['summary' => ['invalid' => 0]]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 1, 'valid' => 1, 'invalid' => 0]],
        ]);

        $this->artisan('validate', [
            'path' => $testFile,
            '--concurrent' => '5',
            '--timeout' => '45'
        ])->assertExitCode(0);

        expect($capturedConfig)->toBeInstanceOf(ValidationConfig::class);
    });

    test('handle displays validation summary', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => [
                'total' => 5,
                'valid' => 4,
                'invalid' => 1,
                'success_rate' => 80.0
            ]],
        ]);

        $this->artisan('validate', ['path' => $testFile])
            ->expectsOutput('📊 Summary:')
            ->expectsOutput('   Total links: 5')
            ->expectsOutput('   Valid: 4')
            ->expectsOutput('   Invalid: 1')
            ->expectsOutput('   Success rate: 80%')
            ->assertExitCode(1); // Should return 1 for invalid links
    });

    test('handle returns success for valid links', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 5, 'valid' => 5, 'invalid' => 0]],
        ]);

        $this->artisan('validate', ['path' => $testFile])
            ->assertExitCode(0);
    });

    test('handle returns failure for invalid links', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 2]]],
        ]);

        $this->setupReportingMock($this->mockReporting, [
            'generateReport' => ['return' => 'Report content'],
            'generateSummary' => ['return' => ['total' => 5, 'valid' => 3, 'invalid' => 2]],
        ]);

        $this->artisan('validate', ['path' => $testFile])
            ->assertExitCode(1);
    });

    test('handle handles validation exceptions', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->mockLinkValidation
            ->shouldReceive('validateFile')
            ->once()
            ->andThrow(new \Exception('Validation failed'));

        $this->artisan('validate', ['path' => $testFile])
            ->expectsOutput('❌ Validation failed: Validation failed')
            ->assertExitCode(1);
    });

    test('handle saves report to file when requested', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $outputFile = $this->getTestPath('report.json');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->mockReporting
            ->shouldReceive('generateReport')
            ->once()
            ->andReturn('Report content');

        $this->mockReporting
            ->shouldReceive('exportReport')
            ->once()
            ->with(Mockery::any(), $outputFile, 'json')
            ->andReturn(true);

        $this->mockReporting
            ->shouldReceive('generateSummary')
            ->once()
            ->andReturn(['total' => 1, 'valid' => 1, 'invalid' => 0]);

        $this->artisan('validate', [
            'path' => $testFile,
            '--format' => 'json',
            '--output' => $outputFile
        ])
            ->expectsOutput("✅ Report saved to: {$outputFile}")
            ->assertExitCode(0);
    });

    test('handle shows error when file save fails', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $outputFile = $this->getTestPath('report.json');

        $this->setupLinkValidationMock($this->mockLinkValidation, [
            'validateFile' => ['return' => ['summary' => ['invalid' => 0]]],
        ]);

        $this->mockReporting
            ->shouldReceive('generateReport')
            ->once()
            ->andReturn('Report content');

        $this->mockReporting
            ->shouldReceive('exportReport')
            ->once()
            ->andReturn(false);

        $this->artisan('validate', [
            'path' => $testFile,
            '--output' => $outputFile
        ])
            ->expectsOutput("❌ Failed to save report to: {$outputFile}")
            ->assertExitCode(1);
    });

    test('handle enters interactive mode when option provided', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsOutput('🔗 Interactive Link Validation Setup')
            ->expectsQuestion('Enter path to validate', $testFile)
            ->expectsConfirmation('Add another path?', 'no')
            ->expectsQuestion('What types of links should be validated?', 'internal,anchor')
            ->expectsQuestion('Choose output format', 'console')
            ->expectsConfirmation('Save output to file?', 'no')
            ->expectsConfirmation('Enable concurrent validation?', 'yes')
            ->expectsQuestion('Maximum concurrent requests', '10')
            ->expectsQuestion('Request timeout (seconds)', '30')
            ->expectsConfirmation('Follow redirects?', 'yes')
            ->expectsQuestion('Maximum redirects to follow', '5')
            ->expectsConfirmation('Cache validation results?', 'yes')
            ->expectsConfirmation('Proceed with validation?', 'no')
            ->expectsOutput('Validation cancelled.')
            ->assertExitCode(1);
    });

    test('interactive mode validates path input', function () {
        $invalidPath = '/nonexistent/path';
        $validPath = $this->createTestFile('valid.md', '# Valid file');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsQuestion('Enter path to validate', $invalidPath)
            ->expectsQuestion('Enter path to validate', $validPath)
            ->expectsConfirmation('Add another path?', 'no')
            ->expectsQuestion('What types of links should be validated?', 'internal')
            ->expectsQuestion('Choose output format', 'console')
            ->expectsConfirmation('Save output to file?', 'no')
            ->expectsConfirmation('Enable concurrent validation?', 'no')
            ->expectsQuestion('Request timeout (seconds)', '30')
            ->expectsConfirmation('Follow redirects?', 'no')
            ->expectsConfirmation('Cache validation results?', 'no')
            ->expectsConfirmation('Proceed with validation?', 'no')
            ->assertExitCode(1);
    });

    test('interactive mode allows multiple paths', function () {
        $file1 = $this->createTestFile('file1.md', '# File 1');
        $file2 = $this->createTestFile('file2.md', '# File 2');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsQuestion('Enter path to validate', $file1)
            ->expectsConfirmation('Add another path?', 'yes')
            ->expectsQuestion('Enter path to validate', $file2)
            ->expectsConfirmation('Add another path?', 'no')
            ->expectsQuestion('What types of links should be validated?', 'internal')
            ->expectsQuestion('Choose output format', 'console')
            ->expectsConfirmation('Save output to file?', 'no')
            ->expectsConfirmation('Enable concurrent validation?', 'no')
            ->expectsQuestion('Request timeout (seconds)', '30')
            ->expectsConfirmation('Follow redirects?', 'no')
            ->expectsConfirmation('Cache validation results?', 'no')
            ->expectsConfirmation('Proceed with validation?', 'no')
            ->assertExitCode(1);
    });

    test('interactive mode shows scope descriptions', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsOutput('📋 Select validation scopes:')
            ->expectsQuestion('Enter path to validate', $testFile)
            ->expectsConfirmation('Add another path?', 'no')
            ->expectsQuestion('What types of links should be validated?', 'external')
            ->expectsQuestion('Choose output format', 'console')
            ->expectsConfirmation('Save output to file?', 'no')
            ->expectsConfirmation('Enable concurrent validation?', 'no')
            ->expectsQuestion('Request timeout (seconds)', '30')
            ->expectsConfirmation('Follow redirects?', 'no')
            ->expectsConfirmation('Cache validation results?', 'no')
            ->expectsConfirmation('Proceed with validation?', 'no')
            ->assertExitCode(1);
    });

    test('interactive mode shows format descriptions', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsOutput('📄 Select output format:')
            ->expectsQuestion('Enter path to validate', $testFile)
            ->expectsConfirmation('Add another path?', 'no')
            ->expectsQuestion('What types of links should be validated?', 'internal')
            ->expectsQuestion('Choose output format', 'json')
            ->expectsConfirmation('Save output to file?', 'no')
            ->expectsConfirmation('Enable concurrent validation?', 'no')
            ->expectsQuestion('Request timeout (seconds)', '30')
            ->expectsConfirmation('Follow redirects?', 'no')
            ->expectsConfirmation('Cache validation results?', 'no')
            ->expectsConfirmation('Proceed with validation?', 'no')
            ->assertExitCode(1);
    });

    test('interactive mode prompts for output file when non console format', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $outputFile = $this->getTestPath('report.json');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsQuestion('Enter path to validate', $testFile)
            ->expectsConfirmation('Add another path?', 'no')
            ->expectsQuestion('What types of links should be validated?', 'internal')
            ->expectsQuestion('Choose output format', 'json')
            ->expectsConfirmation('Save output to file?', 'yes')
            ->expectsQuestion('Output file path', $outputFile)
            ->expectsConfirmation('Enable concurrent validation?', 'no')
            ->expectsQuestion('Request timeout (seconds)', '30')
            ->expectsConfirmation('Follow redirects?', 'no')
            ->expectsConfirmation('Cache validation results?', 'no')
            ->expectsConfirmation('Proceed with validation?', 'no')
            ->assertExitCode(1);
    });

    test('interactive mode validates output file extension', function () {
        $testFile = $this->createTestFile('test.md', '# Test file');
        $wrongExtension = $this->getTestPath('report.txt');
        $correctExtension = $this->getTestPath('report.json');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsQuestion('Enter path to validate', $testFile)
            ->expectsConfirmation('Add another path?', 'no')
            ->expectsQuestion('What types of links should be validated?', 'internal')
            ->expectsQuestion('Choose output format', 'json')
            ->expectsConfirmation('Save output to file?', 'yes')
            ->expectsQuestion('Output file path', $wrongExtension)
            ->expectsQuestion('Output file path', $correctExtension)
            ->expectsConfirmation('Enable concurrent validation?', 'no')
            ->expectsQuestion('Request timeout (seconds)', '30')
            ->expectsConfirmation('Follow redirects?', 'no')
            ->expectsConfirmation('Cache validation results?', 'no')
            ->expectsConfirmation('Proceed with validation?', 'no')
            ->assertExitCode(1);
    });
});
