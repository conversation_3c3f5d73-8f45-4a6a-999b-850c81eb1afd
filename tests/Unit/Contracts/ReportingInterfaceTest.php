<?php

declare(strict_types=1);

namespace Tests\Unit\Contracts;

use App\Services\Contracts\ReportingInterface;
use App\Services\ValueObjects\ValidationConfig;
use Tests\Support\Doubles\Contracts\TestReportingImplementation;
use Tests\Support\Traits\AssertionHelpers;
use Tests\Support\Traits\MockingHelpers;

uses(AssertionHelpers::class, MockingHelpers::class);

describe('ReportingInterface', function () {
    test('interface exists and is interface', function () {
        expect(interface_exists(ReportingInterface::class))->toBeTrue();

        $reflection = new \ReflectionClass(ReportingInterface::class);
        expect($reflection->isInterface())->toBeTrue();
    });

    test('interface has required methods', function () {
        $reflection = new \ReflectionClass(ReportingInterface::class);
        $methods = $reflection->getMethods();
        $methodNames = array_map(fn($method) => $method->getName(), $methods);

        $requiredMethods = [
            'generateReport',
            'generateSummary',
            'exportReport',
        ];

        foreach ($requiredMethods as $method) {
            expect($methodNames)->toContain($method, "Interface should have method: {$method}");
        }
    });

    test('generate report method has correct signature', function () {
        $reflection = new \ReflectionClass(ReportingInterface::class);
        $method = $reflection->getMethod('generateReport');

        expect($method->isPublic())->toBeTrue();
        expect($method->getParameters())->toHaveCount(3);

        $parameters = $method->getParameters();
        expect($parameters[0]->getName())->toBe('results');
        expect($parameters[1]->getName())->toBe('config');
        expect($parameters[2]->getName())->toBe('command');
    });

    test('generate summary method has correct signature', function () {
        $reflection = new \ReflectionClass(ReportingInterface::class);
        $method = $reflection->getMethod('generateSummary');

        expect($method->isPublic())->toBeTrue();
        expect($method->getParameters())->toHaveCount(1);

        $parameters = $method->getParameters();
        expect($parameters[0]->getName())->toBe('results');
    });

    test('export report method has correct signature', function () {
        $reflection = new \ReflectionClass(ReportingInterface::class);
        $method = $reflection->getMethod('exportReport');

        expect($method->isPublic())->toBeTrue();
        expect($method->getParameters())->toHaveCount(3);

        $parameters = $method->getParameters();
        expect($parameters[0]->getName())->toBe('results');
        expect($parameters[1]->getName())->toBe('outputPath');
        expect($parameters[2]->getName())->toBe('format');
    });

    test('interface can be implemented', function () {
        $implementation = new TestReportingImplementation();

        expect($implementation)->toBeInstanceOf(ReportingInterface::class);
    });

    test('implementation returns expected types', function () {
        $implementation = new TestReportingImplementation();
        $config = ValidationConfig::create([]);
        $results = ['summary' => ['total' => 5, 'valid' => 4, 'broken' => 1]];
        $command = $this->createMock(\Illuminate\Console\Command::class);

        $reportResult = $implementation->generateReport($results, $config, $command);
        $summaryResult = $implementation->generateSummary($results);
        $exportResult = $implementation->exportReport($results, '/tmp/report.json', 'json');

        expect($reportResult)->toBeInt();
        expect($summaryResult)->toBeArray();
        expect($exportResult)->toBeBool();
    });

    test('implementation generates report', function () {
        $implementation = new TestReportingImplementation();
        $config = ValidationConfig::create([]);
        $results = ['summary' => ['total' => 5, 'valid' => 4, 'broken' => 1]];
        $command = $this->createMock(\Illuminate\Console\Command::class);

        $exitCode = $implementation->generateReport($results, $config, $command);

        expect($exitCode)->toBeInt();
        expect($exitCode)->toBeGreaterThanOrEqual(0);
    });

    test('implementation generates summary', function () {
        $implementation = new TestReportingImplementation();
        $results = [
            'summary' => [
                'total_links' => 10,
                'valid_links' => 8,
                'broken_links' => 2,
                'execution_time' => 2.5,
            ],
        ];

        $summary = $implementation->generateSummary($results);

        expect($summary)->toBeArray();
        expect($summary)->toHaveKey('total');
        expect($summary)->toHaveKey('valid');
        expect($summary)->toHaveKey('invalid');
    });

    test('implementation exports report', function () {
        $implementation = new TestReportingImplementation();
        $results = ['summary' => ['total' => 5, 'valid' => 4, 'broken' => 1]];

        $success = $implementation->exportReport($results, '/tmp/test-report.json', 'json');

        expect($success)->toBeBool();
    });

    test('interface methods are callable', function () {
        $implementation = new TestReportingImplementation();

        expect(is_callable([$implementation, 'generateReport']))->toBeTrue();
        expect(is_callable([$implementation, 'generateSummary']))->toBeTrue();
        expect(is_callable([$implementation, 'exportReport']))->toBeTrue();
    });

    test('interface has proper namespace', function () {
        $reflection = new \ReflectionClass(ReportingInterface::class);

        expect($reflection->getNamespaceName())->toBe('App\Services\Contracts');
    });

    test('interface can be used for type hinting', function () {
        $implementation = new TestReportingImplementation();

        expect($implementation)->toBeInstanceOf(ReportingInterface::class);

        // Test that all interface methods are implemented
        $reflection = new \ReflectionClass(ReportingInterface::class);
        $interfaceMethods = $reflection->getMethods();

        foreach ($interfaceMethods as $method) {
            expect(method_exists($implementation, $method->getName()))->toBeTrue(
                "Implementation should have method: {$method->getName()}"
            );
        }
    });

    test('mock implementation works correctly', function () {
        $mock = $this->mockReporting();

        $this->setupReportingMock($mock, [
            'generateReport' => ['return' => 0],
            'generateSummary' => ['return' => ['total' => 5, 'valid' => 4, 'invalid' => 1]],
            'exportReport' => ['return' => true],
        ]);

        $config = ValidationConfig::create([]);
        $results = ['summary' => ['total' => 5]];
        $command = $this->createMock(\Illuminate\Console\Command::class);

        $reportResult = $mock->generateReport($results, $config, $command);
        $summaryResult = $mock->generateSummary($results);
        $exportResult = $mock->exportReport($results, '/tmp/report.json', 'json');

        expect($reportResult)->toBe(0);
        expect($summaryResult)->toBeArray();
        expect($exportResult)->toBeTrue();
    });

    test('interface supports different formats', function () {
        $implementation = new TestReportingImplementation();
        $results = ['summary' => ['total' => 5, 'valid' => 4, 'broken' => 1]];

        $formats = ['json', 'html', 'markdown', 'console'];

        foreach ($formats as $format) {
            $success = $implementation->exportReport($results, "/tmp/report.{$format}", $format);
            expect($success)->toBeBool();
        }
    });

    test('interface handles empty results', function () {
        $implementation = new TestReportingImplementation();
        $emptyResults = ['summary' => ['total' => 0, 'valid' => 0, 'broken' => 0]];

        $summary = $implementation->generateSummary($emptyResults);
        $export = $implementation->exportReport($emptyResults, '/tmp/empty-report.json', 'json');

        expect($summary)->toBeArray();
        expect($export)->toBeBool();
    });

});
