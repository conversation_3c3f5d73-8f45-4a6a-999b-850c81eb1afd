<?php

declare(strict_types=1);

namespace Tests\Unit\Contracts;

use App\Services\Contracts\GitHubAnchorInterface;
use App\Services\Contracts\SecurityValidationInterface;
use App\Services\Contracts\StatisticsInterface;
use Tests\Support\Doubles\Contracts\TestGitHubAnchorImplementation;
use Tests\Support\Doubles\Contracts\TestSecurityValidationImplementation;
use Tests\Support\Doubles\Contracts\TestStatisticsImplementation;
use Tests\Support\Traits\AssertionHelpers;
use Tests\Support\Traits\MockingHelpers;

uses(AssertionHelpers::class, MockingHelpers::class);

describe('ServiceContracts', function () {
    test('github anchor interface exists and is interface', function () {
        expect(interface_exists(GitHubAnchorInterface::class))->toBeTrue();

        $reflection = new \ReflectionClass(GitHubAnchorInterface::class);
        expect($reflection->isInterface())->toBeTrue();
    });

    test('security validation interface exists and is interface', function () {
        expect(interface_exists(SecurityValidationInterface::class))->toBeTrue();

        $reflection = new \ReflectionClass(SecurityValidationInterface::class);
        expect($reflection->isInterface())->toBeTrue();
    });

    test('statistics interface exists and is interface', function () {
        expect(interface_exists(StatisticsInterface::class))->toBeTrue();

        $reflection = new \ReflectionClass(StatisticsInterface::class);
        expect($reflection->isInterface())->toBeTrue();
    });

    test('github anchor interface can be implemented', function () {
        $implementation = new TestGitHubAnchorImplementation();

        expect($implementation)->toBeInstanceOf(GitHubAnchorInterface::class);
    });

    test('security validation interface can be implemented', function () {
        $implementation = new TestSecurityValidationImplementation();

        expect($implementation)->toBeInstanceOf(SecurityValidationInterface::class);
    });

    test('statistics interface can be implemented', function () {
        $implementation = new TestStatisticsImplementation();

                expect($implementation)->toBeInstanceOf(StatisticsInterface::class);
    });

    test('github anchor interface has proper namespace', function () {
        $reflection = new \ReflectionClass(GitHubAnchorInterface::class);

        expect($reflection->getNamespaceName())->toBe('App\Services\Contracts');
    });

    test('security validation interface has proper namespace', function () {
        $reflection = new \ReflectionClass(SecurityValidationInterface::class);

        expect($reflection->getNamespaceName())->toBe('App\Services\Contracts');
    });

    test('statistics interface has proper namespace', function () {
        $reflection = new \ReflectionClass(StatisticsInterface::class);

        expect($reflection->getNamespaceName())->toBe('App\Services\Contracts');
    });

    test('github anchor interface can be used for type hinting', function () {
        $implementation = new TestGitHubAnchorImplementation();

        expect($implementation)->toBeInstanceOf(GitHubAnchorInterface::class);
    });

    test('security validation interface can be used for type hinting', function () {
        $implementation = new TestSecurityValidationImplementation();

        expect($implementation)->toBeInstanceOf(SecurityValidationInterface::class);
    });

    test('statistics interface can be used for type hinting', function () {
        $implementation = new TestStatisticsImplementation();

        expect($implementation)->toBeInstanceOf(StatisticsInterface::class);
    });

    test('mock implementations work correctly', function () {
        $githubMock = $this->mockGitHubAnchor();
        $securityMock = $this->mockSecurityValidation();
        $statisticsMock = $this->mockStatistics();

        expect($githubMock)->toBeInstanceOf(GitHubAnchorInterface::class);
        expect($securityMock)->toBeInstanceOf(SecurityValidationInterface::class);
        expect($statisticsMock)->toBeInstanceOf(StatisticsInterface::class);
    });

    test('all interfaces are in correct namespace', function () {
        $interfaces = [
            GitHubAnchorInterface::class,
            SecurityValidationInterface::class,
            StatisticsInterface::class,
        ];

        foreach ($interfaces as $interface) {
            $reflection = new \ReflectionClass($interface);
            expect($reflection->getNamespaceName())->toBe('App\Services\Contracts');
        }
    });

    test('all interfaces are actually interfaces', function () {
        $interfaces = [
            GitHubAnchorInterface::class,
            SecurityValidationInterface::class,
            StatisticsInterface::class,
        ];

        foreach ($interfaces as $interface) {
            $reflection = new \ReflectionClass($interface);
            expect($reflection->isInterface())->toBeTrue("{$interface} should be an interface");
        }
    });

    test('interfaces have methods', function () {
        $interfaces = [
            GitHubAnchorInterface::class,
            SecurityValidationInterface::class,
            StatisticsInterface::class,
        ];

        foreach ($interfaces as $interface) {
            $reflection = new \ReflectionClass($interface);
            $methods = $reflection->getMethods();

            expect(count($methods))->toBeGreaterThan(0, "{$interface} should have at least one method");
        }
    });

    test('interface methods are public', function () {
        $interfaces = [
            GitHubAnchorInterface::class,
            SecurityValidationInterface::class,
            StatisticsInterface::class,
        ];

        foreach ($interfaces as $interface) {
            $reflection = new \ReflectionClass($interface);
            $methods = $reflection->getMethods();

            foreach ($methods as $method) {
                expect($method->isPublic())->toBeTrue(
                    "Method {$method->getName()} in {$interface} should be public"
                );
            }
        }
    });

    test('interface methods are abstract', function () {
        $interfaces = [
            GitHubAnchorInterface::class,
            SecurityValidationInterface::class,
            StatisticsInterface::class,
        ];

        foreach ($interfaces as $interface) {
            $reflection = new \ReflectionClass($interface);
            $methods = $reflection->getMethods();

            foreach ($methods as $method) {
                expect($method->isAbstract())->toBeTrue(
                    "Method {$method->getName()} in {$interface} should be abstract"
                );
            }
        }
    });

});
