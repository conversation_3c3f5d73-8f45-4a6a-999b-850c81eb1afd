{"version": "pest_3.8.2", "defects": {"P\\Tests\\E2e\\RealWorldScenariosTest::__pest_evaluable__Real_World_Link_Validation_Scenarios__→__documentation_website_validation__→_validates_complete_documentation_site_structure": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_it_can_be_instantiated": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_handle_shows_usage_when_no_options_provided": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_handle_with_init_option_creates_configuration_file": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_handle_with_init_option_prompts_for_overwrite_when_file_exists": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_handle_with_init_option_overwrites_when_confirmed": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_handle_with_show_option_displays_current_configuration": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_initialize_config_creates_valid_json_structure": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_initialize_config_sets_check_external_based_on_scope": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_initialize_config_handles_file_write_failure": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_show_config_displays_formatted_json": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_gather_configuration_settings_returns_valid_structure": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_command_signature_is_correctly_defined": 8, "P\\Tests\\Unit\\Commands\\ConfigCommandTest::__pest_evaluable__ConfigCommand__→_command_description_is_set": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_it_can_be_instantiated_with_required_dependencies": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_constructor_sets_dependencies_correctly": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_collect_files_returns_array_for_single_file": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_collect_files_returns_array_for_directory": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_collect_files_handles_mixed_paths": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_validate_command_options_accepts_valid_scope": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_validate_command_options_accepts_all_scope": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_validate_command_options_accepts_comma_separated_scopes": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_validate_command_options_rejects_invalid_scope": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_validate_command_options_rejects_invalid_format": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_process_validation_results_returns_success_for_no_broken_links": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_process_validation_results_returns_failure_for_broken_links": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_execute_validation_calls_link_validation_service": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_execute_validation_handles_exceptions": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_handle_validation_error_returns_error_exit_code": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_handle_validation_error_shows_verbose_output_when_enabled": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_group_links_by_status_organizes_results_correctly": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_display_status_groups_handles_empty_groups": 8, "P\\Tests\\Unit\\Commands\\BaseValidationCommandTest::__pest_evaluable__BaseValidationCommand__→_display_validation_summary_shows_summary_information": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_security_exception_extends_validate_links_exception": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_security_exception_can_be_instantiated": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_security_exception_has_correct_error_code": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_security_exception_has_critical_severity": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_security_exception_can_be_created_with_threat_context": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_security_exception_can_be_created_for_blacklisted_domain": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_security_exception_can_be_created_for_ssl_violation": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_validation_exception_extends_validate_links_exception": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_validation_exception_can_be_instantiated": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_validation_exception_has_correct_error_code": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_validation_exception_has_medium_severity": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_validation_exception_can_be_created_for_link_failure": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_validation_exception_can_be_created_for_file_processing_failure": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_validation_exception_can_be_created_for_timeout": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_handler_extends_laravel_exception_handler": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_handler_can_be_instantiated": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_handler_has_dont_report_property": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_handler_has_dont_flash_property": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_handler_register_method_exists": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_all_exceptions_are_serializable": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_all_exceptions_are_json_serializable": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_all_exceptions_have_valid_error_code_format": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_all_exceptions_have_appropriate_severity_levels": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_exceptions_can_be_distinguished_by_type": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_exceptions_preserve_inheritance_chain": 8, "P\\Tests\\Unit\\Exceptions\\RemainingExceptionsTest::__pest_evaluable__RemainingExceptions__→_exception_factory_methods_work_correctly": 8, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_construct_creates_valid_instance": 8, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_has_expected_constructor_parameters": 7, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_with_scopes_creates_new_instance": 7, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_metadata_handling_works": 7, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_console_formatting_works": 7, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_has_string_representation": 7, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_it_can_be_instantiated": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_handle_executes_successfully": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_handle_accepts_name_argument": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_handle_uses_default_name_when_not_provided": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_handle_method_returns_void": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_signature_is_correctly_defined": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_description_is_set": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_schedule_method_exists_and_accepts_schedule_parameter": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_schedule_method_has_correct_signature": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_schedule_method_returns_void": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_renders_termwind_output": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_is_final_class": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_extends_laravel_zero_command": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_has_proper_namespace": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_uses_strict_types": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_imports_required_classes": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_has_proper_docblocks": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_signature_has_correct_default_value": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_can_be_called_with_different_names": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_handles_empty_name_argument": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_command_handles_special_characters_in_name": 8, "P\\Tests\\Unit\\Commands\\InspireCommandTest::__pest_evaluable__InspireCommand__→_schedule_method_contains_commented_example": 8}, "times": {"P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_construct_creates_valid_instance": 0.013, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_create_factory_method_works": 0.003, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_has_expected_constructor_parameters": 0.002, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_has_expected_getter_methods": 0, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_to_array_returns_expected_structure": 0.001, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_is_serializable": 0, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_validates_input_data": 0, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_equality_works_correctly": 0.001, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_has_scope_method_works": 0, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_get_effective_scopes_returns_correct_scopes": 0, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_with_scopes_creates_new_instance": 0.001, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_with_output_format_creates_new_instance": 0.001, "P\\Tests\\Services\\ValueObjects\\ValidationConfigTest::__pest_evaluable__ValidationConfig__→_with_defaults_applies_default_values": 0.001, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_construct_creates_valid_instance": 0.002, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_success_factory_method_works": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_failure_factory_method_works": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_has_expected_getter_methods": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_to_array_returns_expected_structure": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_to_json_returns_valid_json": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_is_serializable": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_equality_works_correctly": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_status_checking_methods_work": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_metadata_handling_works": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_error_handling_works": 0, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_console_formatting_works": 0.001, "P\\Tests\\Services\\ValueObjects\\ValidationResultTest::__pest_evaluable__ValidationResult__→_has_string_representation": 0}}